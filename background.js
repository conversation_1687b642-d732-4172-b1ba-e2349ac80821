// Service Worker para Manifest V3 - sem window object
let psshs = [];
let requests = [];
let bodys = [];
let targetIds = [];
let pageURL = "";
let clearkey = "";
let isBlock = false;
let blockRules = [];

// Inicializar configurações
chrome.storage.local.get("isBlock", (value) => {
    isBlock = value.isBlock || false;
});

function convertHeaders(obj) {
    return JSON.stringify(Object.fromEntries(obj.map(header => [header.name, header.value])))
}

// Carregar regras de bloqueio
fetch(chrome.runtime.getURL("blockRules.conf"))
    .then((r) => r.text())
    .then((text) => {
        blockRules = text.replace(/\n^\s*$|\s*\/\/.*|\s*$/gm, "").split("\n");
    })
    .catch(() => {
        blockRules = [];
    });

function testBlock(url) {
    return isBlock && blockRules.some(e => url.includes(e));
}

// Receber mensagens do content script
chrome.runtime.onMessage.addListener(
    function (request, sender, sendResponse) {
        switch (request.type) {
            case "RESET":
                // Reiniciar variáveis ao invés de recarregar
                psshs = [];
                requests = [];
                bodys = [];
                targetIds = [];
                pageURL = "";
                clearkey = "";
                break;
            case "PSSH":
                psshs.push(request.text);
                pageURL = sender.tab.url;
                targetIds = [sender.tab.id, sender.frameId];
                break;
            case "CLEARKEY":
                clearkey = request.text;
                break;
            case "LICENSE":
                requests.push(request.data);
                break;
            case "GET_DATA":
                // Para popup acessar os dados
                sendResponse({
                    psshs: psshs,
                    requests: requests,
                    pageURL: pageURL,
                    targetIds: targetIds,
                    clearkey: clearkey
                });
                return true;
            case "FETCH":
                // Proxy para requisições CORS
                fetch(request.u, {
                    method: request.m,
                    headers: JSON.parse(request.h),
                    body: Uint8Array.from(atob(request.b), c => c.charCodeAt(0))
                })
                    .then(r => r.arrayBuffer())
                    .then(r => {
                        sendResponse(btoa(String.fromCharCode(...new Uint8Array(r))));
                    })
                    .catch(err => {
                        sendResponse(null);
                    });
                return true;
        }
    }
);

chrome.action.onClicked.addListener(tab => {
    if (chrome.windows) {
        chrome.windows.create({
            url: "popup/main.html",
            type: "popup",
            width: 820,
            height: 600
        });
    } else {
        chrome.tabs.create({ url: 'popup/main.html' })
    }
});

function createMenu() {
    chrome.storage.local.set({ 'isBlock': false }, null);
    chrome.contextMenus.create({
        id: "toggleBlocking",
        title: "Enable License Blocking"
    });
}

chrome.runtime.onInstalled.addListener(createMenu);
chrome.runtime.onStartup.addListener(createMenu);

chrome.contextMenus.onClicked.addListener(item => {
    if (item.menuItemId == "toggleBlocking") {
        chrome.storage.local.get("isBlock", (value) => {
            if (value.isBlock) {
                chrome.storage.local.set({ 'isBlock': false }, null);
                chrome.contextMenus.update("toggleBlocking", { title: "Enable License Blocking" });
                isBlock = false;
            } else {
                chrome.storage.local.set({ 'isBlock': true }, null);
                chrome.contextMenus.update("toggleBlocking", { title: "Disable License Blocking" });
                isBlock = true;
            }
        });
    }
});