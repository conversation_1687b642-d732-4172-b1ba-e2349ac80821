.hidden {
  display: none;
}

html,
body {
  display: grid;
  height: 100%;
  width: 100%;
  margin: 0; /* Reset default margin */
  padding: 0; /* Reset default padding */
}

body {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background: linear-gradient(45deg, #0d364c, #062535, #02141e);
  grid-template-rows: auto 1fr auto;
}

#noEME {
  justify-self: center;
  align-self: center;
  color: white;
}

#updateNotice {
  justify-self: center;
  align-self: end;
  color: white;
}

#guess,
#sendToCDRM {
  margin-right: 10px;
  padding: 8px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

#guess:hover,
#sendToCDRM:hover {
  background-color: #45a049;
}

#guess:disabled,
#sendToCDRM:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

#updateNotice a {
  color: aqua;
}

#wvForm {
  display: grid;
  grid-template-rows: auto;
  color: white;
}

#wvForm label {
  justify-self: center;
}

#pssh,
#license,
#schemeSelect {
  width: 80%;
  justify-self: center;
}

#psshButton,
#licenseButton,
#editSchemeButton {
  width: 20%;
  justify-self: center;
}

#toggleHistory {
  display: grid;
}

#toggleHistory button {
  width: 20%;
  height: auto;
}

#guess {
  width: 20%;
  justify-self: center;
  margin-top: 5%;
}

#result {
  width: 90%;
  overflow-y: scroll;
  overflow-x: hidden;
  resize: none;
  justify-self: center;
}

#chooserContainer {
  color: white;
}

#ckHome h3,
label {
  color: white;
  justify-self: center;
}

#ckHome label {
  align-self: end;
}

#ckResult {
  width: 90%;
  overflow-y: scroll;
  overflow-x: hidden;
  resize: none;
  justify-self: center;
}
