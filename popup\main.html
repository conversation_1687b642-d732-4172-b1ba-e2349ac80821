<html>
  <head>
    <meta charset="UTF-8">
    <title>Widevine L3 Guessor 2024</title>
    <link rel="stylesheet" href="style.css">
    <script src="/libs/pyodide/pyodide.js"></script>
  </head>
  <body>
    <div id="toggleHistory">
      <a href="./history.html"><button>Show History</button></a>
      <br><br>
    </div>
    <div id="noEME">
      Widevine content hasn't detected in this page.<br>
      Open widevine-protected website and try again!
    </div>
    <div id="home" class="hidden">
      <form id="wvForm">
        <label for="pssh">PSSH</label>
        <input type="text" id="pssh" disabled/>
        <input type="button" id="psshButton" value="Select" /><br>

        <label for="license">License URL</label>
        <input type="text" id="license" disabled/>
        <input type="button" id="licenseButton" value="Select" /><br>

        <label for="schemeSelect">Challenge scheme</label>
        <select id="schemeSelect">
          <option value="Amazon">Amazon</option>
          <option value="Allente">Allente</option>
          <option value="CanalPlusVOD">CanalPlus (VOD)</option>
          <option value="CanalPlusLive">CanalPlus (Live)</option>
          <option value="Comcast">Comcast Xfinity</option>
          <option value="CommonWV" selected>CommonWV</option>
          <option value="DRMToday">DRMToday</option>
          <option value="Fantop">Fantop</option>
          <option value="GlobalTV">GlobalTV</option>
          <option value="Heuristic">Heuristic</option>
          <option value="moTV">moTV</option>
          <option value="NosTV">NosTV</option>
          <option value="oqee">Oqee</option>
          <option value="PolSatBoxGo">PolSatBoxGo</option>
          <option value="RedBee">Red Bee Media</option>
          <option value="Sling">Sling</option>
          <option value="thePlatform">thePlatform</option>
          <option value="VdoCipher">VdoCipher</option>
          <option value="VUDRM">VUDRM</option>
          <option value="Vodafone">Vodafone</option>
          <option value="Youku">Youku</option>
          <option value="YouTube">YouTube</option>
        </select>
        <input type="button" id="editSchemeButton" value="Edit" />

        <input type="button" id="guess" value="Guess!"><br>

        <label for="result">Result:</label><br>
        <textarea id="result" rows="10" cols="50"></textarea>

      </form>
    </div>
    <div id="ckHome" class="hidden">
      <h3>Clearkey detected</h3>
      <label for="ckResult">Result:</label><br>
      <textarea id="ckResult" rows="10" cols="50"></textarea>
    </div>
    <div id="chooserContainer" class="hidden">
      <input type="text" id="chooserSearch" placeholder="Search">
      <ul id="items"></ul>
    </div>
    <div id="editSchemeContainer" class="hidden">
      <textarea id="schemeCode" rows="10" cols="50"></textarea>
      <input type="button" id="editSchemeOK" value="OK" />
    </div>
    <div id="updateNotice" class="hidden">
      Version =VER= update available!
      <a href="https://github.com/FoxRefire/wvg/archive/=HASH=.zip">Download</a>
    </div>
  </body>
  <script src="./main.js" type="module"></script>
  <script src="./drawList.js"></script>
  <script src="./editScheme.js"></script>
  <script src="./updateNotice.js"></script>
</html>
