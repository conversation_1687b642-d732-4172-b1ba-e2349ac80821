// Variables to store data from service worker
let psshs = [];
let requests = [];
var userInputs = {};

// Get data from service worker and initialize event listeners
chrome.runtime.sendMessage({ type: "GET_DATA" }, (response) => {
    if (response) {
        psshs = response.psshs || [];
        requests = response.requests || [];

        // Initialize event listeners after data is available
        document.getElementById('psshButton').addEventListener("click", () => drawList(psshs, 'pssh'));
        document.getElementById('licenseButton').addEventListener("click", () => drawList(requests.map(r => r['url']), 'license'));
    }
});

function writeListElement(items, outputVar, searchStr) {
    document.getElementById("items").innerHTML = '';

    items.forEach((item, index) => {
        if (!searchStr || item.includes(searchStr)) {
            const li = document.createElement('li');
            li.textContent = item;
            li.addEventListener('click', () => itemSelected(index, item, outputVar));
            document.getElementById("items").appendChild(li);
        }
    });
}

function drawList(items, outputVar) {
    document.getElementById('home').style.display = 'none';
    document.getElementById('chooserContainer').style.display = 'grid';
    document.getElementById('toggleHistory').style.display = 'none';

    writeListElement(items, outputVar, null)
    document.getElementById("chooserSearch").addEventListener('input', event => {
        const searchStr = event.target.value.toLowerCase();
        writeListElement(items, outputVar, searchStr)
    });
}

function itemSelected(index, item, outputVar) {
    userInputs[outputVar] = index;
    document.getElementById(outputVar).value = item;
    document.getElementById('chooserContainer').style.display = 'none';
    document.getElementById('home').style.display = 'grid';
    document.getElementById('toggleHistory').style.display = 'grid';
    document.getElementById("chooserSearch").value = ""
}
