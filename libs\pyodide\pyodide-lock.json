{"info": {"arch": "wasm32", "platform": "emscripten_3_1_46", "python": "3.11.3", "version": "0.25.1"}, "packages": {"aiohttp": {"depends": ["aiosignal", "async-timeout", "attrs", "charset-normalizer", "frozenlist", "multidict", "yarl"], "file_name": "aiohttp-3.8.6-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["aiohttp"], "install_dir": "site", "name": "aiohttp", "package_type": "package", "sha256": "5dacfc27095a9e04e179d58dc0bf6462d60db233513553ab82f2b70d948b24a5", "shared_library": false, "unvendored_tests": true, "version": "3.8.6"}, "aiohttp-tests": {"depends": ["aiohttp"], "file_name": "aiohttp-tests.tar", "imports": [], "install_dir": "site", "name": "aiohttp-tests", "package_type": "package", "sha256": "8f2adafe51fa8789f6c5ee5b725f20885835cc2abf4435da88a69c6d9dc93ff5", "shared_library": false, "unvendored_tests": false, "version": "3.8.6"}, "aiosignal": {"depends": ["frozenlist"], "file_name": "aiosignal-1.3.1-py3-none-any.whl", "imports": ["aiosignal"], "install_dir": "site", "name": "aiosignal", "package_type": "package", "sha256": "452e85cb9cb368035b68f9941f648863c8da02e63b7da92bb8d717233d234b7f", "shared_library": false, "unvendored_tests": false, "version": "1.3.1"}, "asciitree": {"depends": [], "file_name": "asciitree-0.3.3-py3-none-any.whl", "imports": ["as<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "as<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "43662d430f07ff43d8d567529c5b1e1f5f3e20629ff34fdcc395f0989ee83697", "shared_library": false, "unvendored_tests": false, "version": "0.3.3"}, "astropy": {"depends": ["distutils", "packaging", "numpy", "pyerfa", "pyyaml"], "file_name": "astropy-5.3.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["astropy"], "install_dir": "site", "name": "astropy", "package_type": "package", "sha256": "81e43a497459149b8fbf03c459a566d0bbe1f502fe18f147a77c190ba47cb8f2", "shared_library": false, "unvendored_tests": false, "version": "5.3.2"}, "async-timeout": {"depends": [], "file_name": "async_timeout-4.0.3-py3-none-any.whl", "imports": ["async_timeout"], "install_dir": "site", "name": "async-timeout", "package_type": "package", "sha256": "3d52b5c8125e267ad0443f1e6db230d19ba027946eb34f8e55de61d99cf46dd2", "shared_library": false, "unvendored_tests": false, "version": "4.0.3"}, "atomicwrites": {"depends": [], "file_name": "atomicwrites-1.4.1-py2.py3-none-any.whl", "imports": ["atomicwrites"], "install_dir": "site", "name": "atomicwrites", "package_type": "package", "sha256": "b03bf1a5eeb95dfe0a2f6ed7cedde7d054031390d33a36238cd855d6048b7bab", "shared_library": false, "unvendored_tests": false, "version": "1.4.1"}, "attrs": {"depends": ["six"], "file_name": "attrs-23.1.0-py3-none-any.whl", "imports": ["attr", "attrs"], "install_dir": "site", "name": "attrs", "package_type": "package", "sha256": "bfeb888b962f07447a7941cafe06de8d396c8ac18995db045f37458834086dd0", "shared_library": false, "unvendored_tests": false, "version": "23.1.0"}, "autograd": {"depends": ["numpy", "future"], "file_name": "autograd-1.6.2-py3-none-any.whl", "imports": ["autograd"], "install_dir": "site", "name": "autograd", "package_type": "package", "sha256": "7b58ac091f658ce7d06ccaa7aa561c5e5e1da1fe74120551ed84b62fbc8cc78e", "shared_library": false, "unvendored_tests": true, "version": "1.6.2"}, "autograd-tests": {"depends": ["autograd"], "file_name": "autograd-tests.tar", "imports": [], "install_dir": "site", "name": "autograd-tests", "package_type": "package", "sha256": "f4791dc86e9843516fe577f4db49eb186402c676f86c07acd0e409f2a019eb4f", "shared_library": false, "unvendored_tests": false, "version": "1.6.2"}, "awkward-cpp": {"depends": ["numpy"], "file_name": "awkward_cpp-26-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["awkward_cpp"], "install_dir": "site", "name": "awkward-cpp", "package_type": "package", "sha256": "2394b0aa6b198242ff717ba7e92532fda878eb578cb24cdd455edf82503fdfd7", "shared_library": false, "unvendored_tests": false, "version": "26"}, "b2d": {"depends": ["numpy", "pydantic", "setuptools"], "file_name": "b2d-0.7.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["b2d"], "install_dir": "site", "name": "b2d", "package_type": "package", "sha256": "149fc0248121cf1b8e5195d1b003bc03b6900ce563b9b64bccd13865593b2b8b", "shared_library": false, "unvendored_tests": false, "version": "0.7.4"}, "bcrypt": {"depends": [], "file_name": "bcrypt-4.0.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["bcrypt"], "install_dir": "site", "name": "bcrypt", "package_type": "package", "sha256": "e9b2d778c6615b2ade88eac01e4b21ec7b4d6b51e05ac3c748aebb12e5f71910", "shared_library": false, "unvendored_tests": false, "version": "4.0.1"}, "beautifulsoup4": {"depends": ["soupsieve"], "file_name": "<PERSON><PERSON>up4-4.12.2-py3-none-any.whl", "imports": ["bs4"], "install_dir": "site", "name": "beautifulsoup4", "package_type": "package", "sha256": "dbe2f94facd8a454d2322368cb11d522d8d16987f4fc28f2dcce5b967fcb749b", "shared_library": false, "unvendored_tests": true, "version": "4.12.2"}, "beautifulsoup4-tests": {"depends": ["beautifulsoup4"], "file_name": "beautifulsoup4-tests.tar", "imports": [], "install_dir": "site", "name": "beautifulsoup4-tests", "package_type": "package", "sha256": "7d6a84548cb6169d89d57e13635bdddda6083f9314032a2baea6aa2d12a9d201", "shared_library": false, "unvendored_tests": false, "version": "4.12.2"}, "biopython": {"depends": ["numpy"], "file_name": "biopython-1.81-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["Bio", "BioSQL"], "install_dir": "site", "name": "biopython", "package_type": "package", "sha256": "370551bd0e7430c2cb41c043780ccd0d1bd76f52ba0d521fc53f8ed55861bc38", "shared_library": false, "unvendored_tests": false, "version": "1.81"}, "bitarray": {"depends": [], "file_name": "bitarray-2.8.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["bitarray"], "install_dir": "site", "name": "bitarray", "package_type": "package", "sha256": "f40653a95a2f1573daef91579d4661a2ec6a7ae59589060f9e87114655cd461c", "shared_library": false, "unvendored_tests": true, "version": "2.8.1"}, "bitarray-tests": {"depends": ["bitarray"], "file_name": "bitarray-tests.tar", "imports": [], "install_dir": "site", "name": "bitarray-tests", "package_type": "package", "sha256": "818b276df6efc4d41f59cc80785a33ff46cceac2b6dd9fbd30eedb503547f9db", "shared_library": false, "unvendored_tests": false, "version": "2.8.1"}, "bitstring": {"depends": ["bitarray"], "file_name": "bitstring-4.1.1-py3-none-any.whl", "imports": ["bitstring"], "install_dir": "site", "name": "bitstring", "package_type": "package", "sha256": "ae6cfeaefce7f828f157a41da419cdfc45f0f9cfc95112789789630bf0594746", "shared_library": false, "unvendored_tests": false, "version": "4.1.1"}, "bleach": {"depends": ["webencodings", "packaging", "six"], "file_name": "bleach-6.0.0-py3-none-any.whl", "imports": ["bleach"], "install_dir": "site", "name": "bleach", "package_type": "package", "sha256": "05156da9303d247f6220038f6e5b8d4c7556dd8a8096034b4ab884c4717c894f", "shared_library": false, "unvendored_tests": false, "version": "6.0.0"}, "bokeh": {"depends": ["distutils", "numpy", "jinja2", "pandas", "pillow", "python-dateutil", "six", "typing-extensions", "pyyaml", "xyzservices"], "file_name": "bokeh-3.2.2-py3-none-any.whl", "imports": ["bokeh"], "install_dir": "site", "name": "bokeh", "package_type": "package", "sha256": "17003e3bdc4fa2bcd44a5d02ad059f58811043744a448e70d05a81d6ba6bda5e", "shared_library": false, "unvendored_tests": false, "version": "3.2.2"}, "boost-histogram": {"depends": ["numpy"], "file_name": "boost_histogram-1.3.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["boost_histogram"], "install_dir": "site", "name": "boost-histogram", "package_type": "package", "sha256": "ce665ab7dcf0587ff27e6c597435e4aaf7b5d6f41f2fb8e1ed7ebbefe61ebac6", "shared_library": false, "unvendored_tests": false, "version": "1.3.2"}, "brotli": {"depends": [], "file_name": "Brotli-1.0.9-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["brotli"], "install_dir": "site", "name": "brotli", "package_type": "package", "sha256": "365b3669eb68f4c02165bd264ccac110a5be339186b13bb4743085b051ece13c", "shared_library": false, "unvendored_tests": false, "version": "1.0.9"}, "cachetools": {"depends": [], "file_name": "cachetools-5.3.1-py3-none-any.whl", "imports": ["cachetools"], "install_dir": "site", "name": "cachetools", "package_type": "package", "sha256": "7eea389b99a6b055c4e781f739524209b0118324511348361ed0df4913e1a524", "shared_library": false, "unvendored_tests": false, "version": "5.3.1"}, "cartopy": {"depends": ["shapely", "pyshp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy"], "file_name": "Cartopy-0.21.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cartopy"], "install_dir": "site", "name": "Cartopy", "package_type": "package", "sha256": "2ef1cc607f5c03465c24e495de3a216c75b34b603f97a78012acaa3e433cbad8", "shared_library": false, "unvendored_tests": true, "version": "0.21.1"}, "cartopy-tests": {"depends": ["cartopy"], "file_name": "Cartopy-tests.tar", "imports": [], "install_dir": "site", "name": "Cartopy-tests", "package_type": "package", "sha256": "7f2b4cc2070bf73e0e68e3d2166c40f25deac5b2135f4fd054ac5db34938c234", "shared_library": false, "unvendored_tests": false, "version": "0.21.1"}, "cbor-diag": {"depends": [], "file_name": "cbor_diag-1.0.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cbor_diag"], "install_dir": "site", "name": "cbor-diag", "package_type": "package", "sha256": "6186e3bd2ef7e96a408d76b33d8d698d5179b714b545b8cfa39d86a26c2ca2f4", "shared_library": false, "unvendored_tests": false, "version": "1.0.1"}, "certifi": {"depends": [], "file_name": "certifi-2023.7.22-py3-none-any.whl", "imports": ["certifi"], "install_dir": "site", "name": "certifi", "package_type": "package", "sha256": "035e16956f8e00bde9f2da068fac939b6c2eae40af132f6e0c4778f0d459efa1", "shared_library": false, "unvendored_tests": false, "version": "2023.7.22"}, "cffi": {"depends": ["pyc<PERSON><PERSON>"], "file_name": "cffi-1.15.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cffi"], "install_dir": "site", "name": "cffi", "package_type": "package", "sha256": "7302fca160c66d0d2e3a08b2e4310c16f1f8b07368e4a5efe07bf5d903057c3a", "shared_library": false, "unvendored_tests": false, "version": "1.15.1"}, "cffi-example": {"depends": ["cffi"], "file_name": "cffi_example-0.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cffi_example"], "install_dir": "site", "name": "cffi_example", "package_type": "package", "sha256": "c4f8d9ebf5d886862124d5780795911dbe2eb01150588d25d620047bc12a6ac7", "shared_library": false, "unvendored_tests": false, "version": "0.1"}, "cftime": {"depends": ["numpy"], "file_name": "cftime-1.6.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cftime"], "install_dir": "site", "name": "cftime", "package_type": "package", "sha256": "6868baa7e1e1184ff97c3cc8c8c3bdbc99137109d7d07199741ea4500672fe10", "shared_library": false, "unvendored_tests": false, "version": "1.6.2"}, "charset-normalizer": {"depends": [], "file_name": "charset_normalizer-3.3.2-py3-none-any.whl", "imports": ["charset_normalizer"], "install_dir": "site", "name": "charset-normalizer", "package_type": "package", "sha256": "4bd1d6d54704ffa7fb3235f22a11dcbbdf45ade28fc51c5329596b889886a772", "shared_library": false, "unvendored_tests": false, "version": "3.3.2"}, "click": {"depends": [], "file_name": "click-8.1.7-py3-none-any.whl", "imports": ["click"], "install_dir": "site", "name": "click", "package_type": "package", "sha256": "fcf2043ed2309bce8cbd147868bd539f31648c4618d6288242e5dc1040bbffb2", "shared_library": false, "unvendored_tests": false, "version": "8.1.7"}, "cligj": {"depends": ["click"], "file_name": "cligj-0.7.2-py3-none-any.whl", "imports": ["cligj"], "install_dir": "site", "name": "cligj", "package_type": "package", "sha256": "153633543becaf42b7d668d2f6622df8ef81a60d9f7a6236bae9fcd958bc59f9", "shared_library": false, "unvendored_tests": false, "version": "0.7.2"}, "cloudpickle": {"depends": [], "file_name": "cloudpickle-2.2.1-py3-none-any.whl", "imports": ["cloudpickle"], "install_dir": "site", "name": "cloudpickle", "package_type": "package", "sha256": "ed461402550e3fe74ca3ce86e6176a6fb82062bba4802860e558b965af5894fc", "shared_library": false, "unvendored_tests": false, "version": "2.2.1"}, "cmyt": {"depends": ["colorspacious", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more-itertools", "numpy"], "file_name": "cmyt-1.3.1-py3-none-any.whl", "imports": ["cmyt"], "install_dir": "site", "name": "cmyt", "package_type": "package", "sha256": "2967c67e358291863d5ffb724b5d09800339ea3ba3921409d0f31d83406e228e", "shared_library": false, "unvendored_tests": true, "version": "1.3.1"}, "cmyt-tests": {"depends": ["cmyt"], "file_name": "cmyt-tests.tar", "imports": [], "install_dir": "site", "name": "cmyt-tests", "package_type": "package", "sha256": "32e14b49bbb8957a1a5d5f313c0208addde3e1fa4f464b5ce7d84334f6b4d086", "shared_library": false, "unvendored_tests": false, "version": "1.3.1"}, "colorspacious": {"depends": ["numpy"], "file_name": "colorspacious-1.1.2-py2.py3-none-any.whl", "imports": ["colorspacious"], "install_dir": "site", "name": "colorspacious", "package_type": "package", "sha256": "0032bbe174dc9cd644eb8fd6dc96b7887165b17c7bdaa1054f4afcaeed93dbc6", "shared_library": false, "unvendored_tests": false, "version": "1.1.2"}, "contourpy": {"depends": ["numpy"], "file_name": "contourpy-1.2.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["contourpy"], "install_dir": "site", "name": "contourpy", "package_type": "package", "sha256": "212a04720a2d0114dd749ba5d573bcdf99d11319420f87dec4adfb3f5934bb0c", "shared_library": false, "unvendored_tests": false, "version": "1.2.0"}, "coolprop": {"depends": ["numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "CoolProp-6.6.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["CoolProp"], "install_dir": "site", "name": "coolprop", "package_type": "package", "sha256": "5efcf0db5f31ef8624fa19078b2bfb0ed050de7d7dc60b750f65ed31745a9a5b", "shared_library": false, "unvendored_tests": true, "version": "6.6.0"}, "coolprop-tests": {"depends": ["coolprop"], "file_name": "coolprop-tests.tar", "imports": [], "install_dir": "site", "name": "coolprop-tests", "package_type": "package", "sha256": "730b9e72e8d92b96ee5f3a891b9ae9a268d472385460f19ec50ab4de17c05354", "shared_library": false, "unvendored_tests": false, "version": "6.6.0"}, "coverage": {"depends": ["sqlite3"], "file_name": "coverage-7.3.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["coverage"], "install_dir": "site", "name": "coverage", "package_type": "package", "sha256": "547eafbf0472418f89947c5b87d39bf75658f3409ec17dfad6664d94324690f6", "shared_library": false, "unvendored_tests": false, "version": "7.3.0"}, "cpp-exceptions-test": {"depends": [], "file_name": "cpp-exceptions-test-0.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "cpp-exceptions-test", "package_type": "shared_library", "sha256": "d64cb2fe52113b911fce4d276c507e2235e1c1987ac1fd7ab4afa83a28e309a7", "shared_library": true, "unvendored_tests": false, "version": "0.1"}, "cramjam": {"depends": [], "file_name": "cramjam-2.6.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cramjam"], "install_dir": "site", "name": "cramjam", "package_type": "package", "sha256": "72e0c6ab487caa0608f9a858c1bee9bffd91e7d2e540833b249021c3083acb5d", "shared_library": false, "unvendored_tests": false, "version": "2.6.2"}, "cryptography": {"depends": ["openssl", "six", "cffi"], "file_name": "cryptography-39.0.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cryptography"], "install_dir": "site", "name": "cryptography", "package_type": "package", "sha256": "1242bb2e27e84c59e84c5f9ad797c733778e17b43154da74b335aeb95ce82c02", "shared_library": false, "unvendored_tests": false, "version": "39.0.2"}, "cssselect": {"depends": [], "file_name": "cssselect-1.2.0-py2.py3-none-any.whl", "imports": ["cssselect"], "install_dir": "site", "name": "cssselect", "package_type": "package", "sha256": "18b9017b19489fc8c5b2ac239b59aed51315feafc473150815f2ea8c3076e56c", "shared_library": false, "unvendored_tests": false, "version": "1.2.0"}, "cycler": {"depends": ["six"], "file_name": "cycler-0.11.0-py3-none-any.whl", "imports": ["cycler"], "install_dir": "site", "name": "cycler", "package_type": "package", "sha256": "fb8c2099d30eccb6a28081968622a6b641eaccbc50c8e0fff3b89f9c315f74ca", "shared_library": false, "unvendored_tests": false, "version": "0.11.0"}, "cytoolz": {"depends": ["nose", "toolz"], "file_name": "cytoolz-0.12.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cytoolz"], "install_dir": "site", "name": "cytoolz", "package_type": "package", "sha256": "a34ee8f0d0d016a6f5820faa3e56f863cf4ab5c77473077403071e6b14ef0605", "shared_library": false, "unvendored_tests": true, "version": "0.12.2"}, "cytoolz-tests": {"depends": ["cytoolz"], "file_name": "cytoolz-tests.tar", "imports": [], "install_dir": "site", "name": "cytoolz-tests", "package_type": "package", "sha256": "cb3d851b192e2e1d01923ca503cda30a076df1e7b5322c612d052b5b336ad29d", "shared_library": false, "unvendored_tests": false, "version": "0.12.2"}, "decorator": {"depends": [], "file_name": "decorator-5.1.1-py3-none-any.whl", "imports": ["decorator"], "install_dir": "site", "name": "decorator", "package_type": "package", "sha256": "8cb05ef07cfd69ce2524789dff0f71898e70f1df46cd26bff8c0a344058d447f", "shared_library": false, "unvendored_tests": false, "version": "5.1.1"}, "demes": {"depends": ["attrs", "ruamel.yaml"], "file_name": "demes-0.2.3-py3-none-any.whl", "imports": ["demes"], "install_dir": "site", "name": "demes", "package_type": "package", "sha256": "35800f72738148641d3aed2bf2b28aab4d7269facc4393a16faa145f6a1e5fec", "shared_library": false, "unvendored_tests": false, "version": "0.2.3"}, "deprecation": {"depends": ["packaging"], "file_name": "deprecation-2.1.0-py2.py3-none-any.whl", "imports": ["deprecation"], "install_dir": "site", "name": "deprecation", "package_type": "package", "sha256": "a778673281a6c69b2b84c38add2d9e4229a2a4792d4bde448529ac19b0ca0c81", "shared_library": false, "unvendored_tests": false, "version": "2.1.0"}, "distlib": {"depends": [], "file_name": "distlib-0.3.7-py2.py3-none-any.whl", "imports": ["distlib"], "install_dir": "site", "name": "distlib", "package_type": "package", "sha256": "6a9648d64686e42becb258a718635daf4ed23171722e1694e908180407f03d87", "shared_library": false, "unvendored_tests": false, "version": "0.3.7"}, "distutils": {"depends": [], "file_name": "distutils-1.0.0.zip", "imports": ["distutils"], "install_dir": "stdlib", "name": "distutils", "package_type": "cpython_module", "sha256": "b75494eafa9f01d738f7d76846dd92cbf5ce2ebc5e360898a62720c0c1d0817c", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "docutils": {"depends": [], "file_name": "docutils-0.20.1-py3-none-any.whl", "imports": ["docutils"], "install_dir": "site", "name": "docutils", "package_type": "package", "sha256": "6c1288c649310f8fca88b68bb9626c057dde7adf0da3e9c83ec0a26fa3d56d8b", "shared_library": false, "unvendored_tests": false, "version": "0.20.1"}, "exceptiongroup": {"depends": [], "file_name": "exceptiongroup-1.1.3-py3-none-any.whl", "imports": ["exceptiongroup"], "install_dir": "site", "name": "exceptiongroup", "package_type": "package", "sha256": "5c5cbd3b2ba528fb3903756ac1818107705dfb432257cc0b02dd1da3d981b1c2", "shared_library": false, "unvendored_tests": false, "version": "1.1.3"}, "fastparquet": {"depends": ["cramjam", "numpy", "pandas", "fsspec", "packaging"], "file_name": "fastparquet-2023.7.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["fastparquet"], "install_dir": "site", "name": "fastparquet", "package_type": "package", "sha256": "aa64c2029db98a10d9b6c56c3b4e57fe5e720a298338d3048e5a564b28b1ee9f", "shared_library": false, "unvendored_tests": false, "version": "2023.7.0"}, "fiona": {"depends": ["attrs", "certifi", "setuptools", "six", "click", "cligj"], "file_name": "Fiona-1.8.22-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["fiona"], "install_dir": "site", "name": "fiona", "package_type": "package", "sha256": "d9d27c11c6406c5366e3ec1dce47e46cf4c545ac9c6ff2e847e9217e7db11870", "shared_library": false, "unvendored_tests": false, "version": "1.8.22"}, "fonttools": {"depends": [], "file_name": "fonttools-4.42.1-py3-none-any.whl", "imports": ["fontTools"], "install_dir": "site", "name": "fonttools", "package_type": "package", "sha256": "44c48ebb8e54253bee8af587bf2685a16e00860377a2a1cf63cff5532e87a437", "shared_library": false, "unvendored_tests": false, "version": "4.42.1"}, "fpcast-test": {"depends": [], "file_name": "fpcast_test-0.1.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["fpcast_test"], "install_dir": "site", "name": "fpcast-test", "package_type": "package", "sha256": "0a897910554decc8c2af7f9e5a3c05bbfd5e4a4ad759539f6e8bd7d7bf87d59a", "shared_library": false, "unvendored_tests": false, "version": "0.1.1"}, "freesasa": {"depends": [], "file_name": "freesasa-2.2.0.post3-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["freesasa"], "install_dir": "site", "name": "freesasa", "package_type": "package", "sha256": "673fa633c43fa28ecd7661327f7f4cedd7c45a709735fc9b08d6dd46e9a0c6b5", "shared_library": false, "unvendored_tests": false, "version": "2.2.0.post3"}, "frozenlist": {"depends": [], "file_name": "frozenlist-1.4.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["frozenlist"], "install_dir": "site", "name": "frozenlist", "package_type": "package", "sha256": "8510a99c51ccf7ba137a78184559571415902fb3b502d1976604e4722df77008", "shared_library": false, "unvendored_tests": false, "version": "1.4.0"}, "fsspec": {"depends": [], "file_name": "fsspec-2023.6.0-py3-none-any.whl", "imports": ["fsspec"], "install_dir": "site", "name": "fsspec", "package_type": "package", "sha256": "8d032f447841f000f4e2db022bc68a6cfd0b895cdc6bd6c54e3a75cdafc4974e", "shared_library": false, "unvendored_tests": true, "version": "2023.6.0"}, "fsspec-tests": {"depends": ["fsspec"], "file_name": "fsspec-tests.tar", "imports": [], "install_dir": "site", "name": "fsspec-tests", "package_type": "package", "sha256": "28c1ceda3eb9abba21131a5edad6729fbc402e15d8809d0e63529c9471db5d31", "shared_library": false, "unvendored_tests": false, "version": "2023.6.0"}, "future": {"depends": [], "file_name": "future-0.18.3-py3-none-any.whl", "imports": ["future"], "install_dir": "site", "name": "future", "package_type": "package", "sha256": "f8304342e4be16ecf428aae0f58e6fbff1b88a94b420c6858f68e84c8adeeb6d", "shared_library": false, "unvendored_tests": true, "version": "0.18.3"}, "future-tests": {"depends": ["future"], "file_name": "future-tests.tar", "imports": [], "install_dir": "site", "name": "future-tests", "package_type": "package", "sha256": "db020f5e65d1decafd3162f973c02db7411d45881dd42f54199d03146a6071c8", "shared_library": false, "unvendored_tests": false, "version": "0.18.3"}, "galpy": {"depends": ["numpy", "scipy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "astropy", "future", "setuptools"], "file_name": "galpy-1.9.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["galpy"], "install_dir": "site", "name": "galpy", "package_type": "package", "sha256": "e5331bdcfdcdff50ede4b0b485b5e4fa0c8f5d1bcde9b41177f49ccbc7d1d883", "shared_library": false, "unvendored_tests": false, "version": "1.9.0"}, "gdal": {"depends": ["geos"], "file_name": "gdal-3.5.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "gdal", "package_type": "shared_library", "sha256": "928ca6712477d3a4e2e53d4ec7b37e50b72a77ec2e318ee31b506652b106ac84", "shared_library": true, "unvendored_tests": false, "version": "3.5.1"}, "gensim": {"depends": ["numpy", "scipy", "six", "smart_open"], "file_name": "gensim-4.3.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["gensim"], "install_dir": "site", "name": "gensim", "package_type": "package", "sha256": "693314a9010bc570a85790c33431ef403db5a597da4afb198d52efbd241d32e7", "shared_library": false, "unvendored_tests": true, "version": "4.3.1"}, "gensim-tests": {"depends": ["gensim"], "file_name": "gensim-tests.tar", "imports": [], "install_dir": "site", "name": "gensim-tests", "package_type": "package", "sha256": "8d5086ac15d5d8709a809a9438be8cb9912ccb0073e9304f4dd67064f0d471ea", "shared_library": false, "unvendored_tests": false, "version": "4.3.1"}, "geopandas": {"depends": ["shapely", "fiona", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "packaging", "pandas"], "file_name": "geopandas-0.13.2-py3-none-any.whl", "imports": ["geopandas"], "install_dir": "site", "name": "geopandas", "package_type": "package", "sha256": "8770de3ec6e78915f4af15d27cb50d6c3dbeb158fcf0e580de9e72142cd4b938", "shared_library": false, "unvendored_tests": true, "version": "0.13.2"}, "geopandas-tests": {"depends": ["geopandas"], "file_name": "geopandas-tests.tar", "imports": [], "install_dir": "site", "name": "geopandas-tests", "package_type": "package", "sha256": "482923f2be7952335340e5ed177f573532e8fbb14fbfdd446e64eefa0ba6b0bb", "shared_library": false, "unvendored_tests": false, "version": "0.13.2"}, "geos": {"depends": [], "file_name": "geos-3.10.3.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "geos", "package_type": "shared_library", "sha256": "cc272817d15a58e0528e04fe7438b28a6326efac75ef30d202afafe5e6a3d78d", "shared_library": true, "unvendored_tests": false, "version": "3.10.3"}, "gmpy2": {"depends": [], "file_name": "gmpy2-2.1.5-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["gmpy2"], "install_dir": "site", "name": "gmpy2", "package_type": "package", "sha256": "c7fdc25deb1bfcf2134f0908efd3766791859f2cfb59471a5dc0f8dd7989924e", "shared_library": false, "unvendored_tests": false, "version": "2.1.5"}, "gsw": {"depends": ["numpy"], "file_name": "gsw-3.6.16-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["gsw"], "install_dir": "site", "name": "gsw", "package_type": "package", "sha256": "322e02d2cc4b7ec6fe109c93994abcd501177231652f6d3f190a1b4f130bc07b", "shared_library": false, "unvendored_tests": true, "version": "3.6.16"}, "gsw-tests": {"depends": ["gsw"], "file_name": "gsw-tests.tar", "imports": [], "install_dir": "site", "name": "gsw-tests", "package_type": "package", "sha256": "0225cb0576fd4f00da8dd8f05a74f5999f4901cd95d95c12da8c018f0d6a87ee", "shared_library": false, "unvendored_tests": false, "version": "3.6.16"}, "h5py": {"depends": ["numpy", "pkgconfig"], "file_name": "h5py-3.7.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["h5py"], "install_dir": "site", "name": "h5py", "package_type": "package", "sha256": "284f4f4db445a047bbd1f5ef4f85b03ab90710ae719f500724725253ed52a87c", "shared_library": false, "unvendored_tests": true, "version": "3.7.0"}, "h5py-tests": {"depends": ["h5py"], "file_name": "h5py-tests.tar", "imports": [], "install_dir": "site", "name": "h5py-tests", "package_type": "package", "sha256": "439141ffaa93c6a569a6b1214d6953d3d4b8f4e9251ad6dfcddb3120fc5dc180", "shared_library": false, "unvendored_tests": false, "version": "3.7.0"}, "hashlib": {"depends": ["openssl"], "file_name": "hashlib-1.0.0.zip", "imports": ["_hashlib"], "install_dir": "stdlib", "name": "<PERSON><PERSON><PERSON>", "package_type": "cpython_module", "sha256": "96545d5e075cdbfdd26689b56bd4cd6bd705636043e721db3f19f0994ff73a61", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "html5lib": {"depends": ["webencodings", "six"], "file_name": "html5lib-1.1-py2.py3-none-any.whl", "imports": ["html5lib"], "install_dir": "site", "name": "html5lib", "package_type": "package", "sha256": "de95ce171249cec12df3b4586e98637fb49d2c61c6796dd4e92e46d9e8dbe4dd", "shared_library": false, "unvendored_tests": false, "version": "1.1"}, "idna": {"depends": [], "file_name": "idna-3.4-py3-none-any.whl", "imports": ["idna"], "install_dir": "site", "name": "idna", "package_type": "package", "sha256": "b8641b6f6de5306de629259178f431ac30254b357a20e4f82a75e0c612d69672", "shared_library": false, "unvendored_tests": false, "version": "3.4"}, "igraph": {"depends": ["texttable"], "file_name": "igraph-0.10.6-cp39-abi3-emscripten_3_1_46_wasm32.whl", "imports": ["igraph"], "install_dir": "site", "name": "igraph", "package_type": "package", "sha256": "512af240864de8da3c2aac61c507ccefbfcbeac3c6bbb7b853f9eeaa561b4771", "shared_library": false, "unvendored_tests": false, "version": "0.10.6"}, "imageio": {"depends": ["numpy", "pillow"], "file_name": "imageio-2.31.1-py3-none-any.whl", "imports": ["imageio"], "install_dir": "site", "name": "imageio", "package_type": "package", "sha256": "dcbcfd1c1dfaf27dfa9d9398723fe7f3c6c6bf7d76306cc13dc4c99e55645d9b", "shared_library": false, "unvendored_tests": false, "version": "2.31.1"}, "iniconfig": {"depends": [], "file_name": "iniconfig-2.0.0-py3-none-any.whl", "imports": ["iniconfig"], "install_dir": "site", "name": "iniconfig", "package_type": "package", "sha256": "55829da7bce5e900f4e1027b0160010a5f19415e3a1580fa9122499f93d08df5", "shared_library": false, "unvendored_tests": false, "version": "2.0.0"}, "jedi": {"depends": ["parso"], "file_name": "jedi-0.19.0-py2.py3-none-any.whl", "imports": ["jedi"], "install_dir": "site", "name": "jedi", "package_type": "package", "sha256": "201728f959e5244ab625a6818051be174a826a87c6df566acf8db1dba0a4d8db", "shared_library": false, "unvendored_tests": true, "version": "0.19.0"}, "jedi-tests": {"depends": ["jedi"], "file_name": "jedi-tests.tar", "imports": [], "install_dir": "site", "name": "jedi-tests", "package_type": "package", "sha256": "90203b4f196c1a6680fa3bdd0930d11d047df3d739c660ae28a4e47b007975db", "shared_library": false, "unvendored_tests": false, "version": "0.19.0"}, "jinja2": {"depends": ["markupsafe"], "file_name": "Jinja2-3.1.2-py3-none-any.whl", "imports": ["jinja2"], "install_dir": "site", "name": "Jinja2", "package_type": "package", "sha256": "3d36e0ef8fb8f0c360a8987cf6544d54df95ef557fc7d1ada00698ec115cee03", "shared_library": false, "unvendored_tests": false, "version": "3.1.2"}, "joblib": {"depends": ["distutils"], "file_name": "joblib-1.3.2-py3-none-any.whl", "imports": ["joblib"], "install_dir": "site", "name": "joblib", "package_type": "package", "sha256": "e5fa1a44f8dca5780346f00f1698861d846d4b6365c780a61826adcd299ae740", "shared_library": false, "unvendored_tests": true, "version": "1.3.2"}, "joblib-tests": {"depends": ["joblib"], "file_name": "joblib-tests.tar", "imports": [], "install_dir": "site", "name": "joblib-tests", "package_type": "package", "sha256": "1eb0636fa5a6c4ba727e35135587885b7f83d934be2c010b2ea2fd8589e9ef8e", "shared_library": false, "unvendored_tests": false, "version": "1.3.2"}, "jsonschema": {"depends": ["attrs", "pyrsistent"], "file_name": "jsonschema-4.17.3-py3-none-any.whl", "imports": ["jsonschema"], "install_dir": "site", "name": "jsonschema", "package_type": "package", "sha256": "ce29a46d382227b0243574e9993712283e890c88f33eaf511a16837d25e9ad9a", "shared_library": false, "unvendored_tests": true, "version": "4.17.3"}, "jsonschema-tests": {"depends": ["jsonschema"], "file_name": "jsonschema-tests.tar", "imports": [], "install_dir": "site", "name": "jsonschema-tests", "package_type": "package", "sha256": "f5f8356cfc0d28875fffe1e54180f8929d9856f52a4b6a92d88903c7dc648efb", "shared_library": false, "unvendored_tests": false, "version": "4.17.3"}, "kiwisolver": {"depends": [], "file_name": "kiwisolver-1.4.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["kiwisolver"], "install_dir": "site", "name": "kiwisolver", "package_type": "package", "sha256": "8b6330276ee0c47dabbab6a84a4c7411cfc5d7aee3e1686cf561d6a3cb3bd042", "shared_library": false, "unvendored_tests": false, "version": "1.4.4"}, "lazy-loader": {"depends": [], "file_name": "lazy_loader-0.3-py3-none-any.whl", "imports": ["lazy_loader"], "install_dir": "site", "name": "lazy_loader", "package_type": "package", "sha256": "d9c3b21a25ac46cbec8e4a26a7d9ac0f2fa59e3caa0ef83f0c16ea501ef74f04", "shared_library": false, "unvendored_tests": true, "version": "0.3"}, "lazy-loader-tests": {"depends": ["lazy_loader"], "file_name": "lazy_loader-tests.tar", "imports": [], "install_dir": "site", "name": "lazy_loader-tests", "package_type": "package", "sha256": "946f97e09a2681dd7b72076cc31c623d9f557ed8f20ed254c3830f90dcc1bd81", "shared_library": false, "unvendored_tests": false, "version": "0.3"}, "lazy-object-proxy": {"depends": [], "file_name": "lazy_object_proxy-1.9.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["lazy_object_proxy"], "install_dir": "site", "name": "lazy-object-proxy", "package_type": "package", "sha256": "b7ac7ec5737ceeb081d2939874fcbfdf8d0805ac29b56d3bc860bfda33e9c580", "shared_library": false, "unvendored_tests": false, "version": "1.9.0"}, "libhdf5": {"depends": [], "file_name": "libhdf5-1.12.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libhdf5", "package_type": "shared_library", "sha256": "50a7e63e1d00ec776ef7908410231425cf3b8c96affc3a216657fb318e85d6d6", "shared_library": true, "unvendored_tests": false, "version": "1.12.1"}, "libheif": {"depends": [], "file_name": "libheif-1.12.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "lib<PERSON><PERSON>", "package_type": "shared_library", "sha256": "063b073832632ee58cf5931bad45721d00032bb3c6c98f5bdbca77de52b2961e", "shared_library": true, "unvendored_tests": false, "version": "1.12.0"}, "libmagic": {"depends": [], "file_name": "libmagic-5.42.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libmagic", "package_type": "shared_library", "sha256": "dd6eb6b211961309cf05d2f56b0d37168fef6323c6ce7f7c7f990be540fc2c6a", "shared_library": true, "unvendored_tests": false, "version": "5.42"}, "libnetcdf": {"depends": [], "file_name": "libnetcdf-4.9.2.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libnetcdf", "package_type": "shared_library", "sha256": "12c1848ac76e6812dc4cb6e9cf98dd4202d205b8c3f28b9124d23b777336464e", "shared_library": true, "unvendored_tests": false, "version": "4.9.2"}, "lightgbm": {"depends": ["numpy", "scipy", "scikit-learn"], "file_name": "lightgbm-4.3.0-py3-none-emscripten_3_1_46_wasm32.whl", "imports": ["lightgbm"], "install_dir": "site", "name": "lightgbm", "package_type": "package", "sha256": "cfc17222a911f0ed4b290e8dae6c009735b4e34b4e5560ea8c5afa99f4db061c", "shared_library": false, "unvendored_tests": false, "version": "4.3.0"}, "logbook": {"depends": [], "file_name": "Logbook-1.6.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["logbook"], "install_dir": "site", "name": "logbook", "package_type": "package", "sha256": "433801f3e66706f15d5939a662814e3786131fd934de33f6935803fd88a05d1c", "shared_library": false, "unvendored_tests": false, "version": "1.6.0"}, "lxml": {"depends": [], "file_name": "lxml-4.9.3-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["lxml"], "install_dir": "site", "name": "lxml", "package_type": "package", "sha256": "b0591abd915683c44f04f128195aaea3f04f9406045e5b12101dea8c537bbd2e", "shared_library": false, "unvendored_tests": false, "version": "4.9.3"}, "lzma": {"depends": [], "file_name": "lzma-1.0.0.zip", "imports": ["lzma", "_lzma"], "install_dir": "stdlib", "name": "lzma", "package_type": "cpython_module", "sha256": "4bd809931c88480b9628dfd4d4c6488cc86e0b5ec06ebc56c800fe7f7bf7e8d4", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "markupsafe": {"depends": [], "file_name": "MarkupSafe-2.1.3-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["markupsafe"], "install_dir": "site", "name": "MarkupSafe", "package_type": "package", "sha256": "6560ed9785c0b0bd722d5967e90847780d4b9b561f95024a1b7ae759da1ddb25", "shared_library": false, "unvendored_tests": false, "version": "2.1.3"}, "matplotlib": {"depends": ["cycler", "fonttools", "kiwisolver", "numpy", "packaging", "pillow", "pyparsing", "python-dateutil", "pytz", "matplotlib-pyodide"], "file_name": "matplotlib-3.5.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pylab", "mpl_toolkits", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "f7bfdbc3653a9f7bc7273d6ebd4f5f770cd08c88854ff3e66c5dd62395568f72", "shared_library": false, "unvendored_tests": true, "version": "3.5.2"}, "matplotlib-pyodide": {"depends": [], "file_name": "matplotlib_pyodide-0.2.0-py3-none-any.whl", "imports": ["matplotlib_pyodide"], "install_dir": "site", "name": "matplotlib-pyodide", "package_type": "package", "sha256": "ef09d7009e902a3ba385d6be761f960c003e4ebf43140bd1458cedfd9b0c2a74", "shared_library": false, "unvendored_tests": false, "version": "0.2.0"}, "matplotlib-tests": {"depends": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "matplotlib-tests.tar", "imports": [], "install_dir": "site", "name": "matplotlib-tests", "package_type": "package", "sha256": "a703adecc0e9a9c8743e2fa1fda080dbfffdfef66fef2fd2002599b53f53ae5c", "shared_library": false, "unvendored_tests": false, "version": "3.5.2"}, "micropip": {"depends": ["packaging"], "file_name": "micropip-0.5.0-py3-none-any.whl", "imports": ["micropip"], "install_dir": "site", "name": "micropip", "package_type": "package", "sha256": "8df6295822c842e312c71eee20c8561343b7a8f3b0f6a5debb91d390ae0d7d1e", "shared_library": false, "unvendored_tests": false, "version": "0.5.0"}, "mne": {"depends": ["distutils", "numpy", "scipy", "setuptools", "decorator"], "file_name": "mne-1.5.0-py3-none-any.whl", "imports": ["mne"], "install_dir": "site", "name": "mne", "package_type": "package", "sha256": "bb498b873427f55be8f72a252177a465e4710ef2c6c4f7958039635fa699d55e", "shared_library": false, "unvendored_tests": true, "version": "1.5.0"}, "mne-tests": {"depends": ["mne"], "file_name": "mne-tests.tar", "imports": [], "install_dir": "site", "name": "mne-tests", "package_type": "package", "sha256": "df384a92e3574942c92e5964f472cfa28d5591b519163de0927eb63444d17f55", "shared_library": false, "unvendored_tests": false, "version": "1.5.0"}, "more-itertools": {"depends": [], "file_name": "more_itertools-10.1.0-py3-none-any.whl", "imports": ["more_itertools"], "install_dir": "site", "name": "more-itertools", "package_type": "package", "sha256": "78070ab96535d60bd99847d20bffcec44af6ec504ae3c92f74c1fc4df2dc333a", "shared_library": false, "unvendored_tests": false, "version": "10.1.0"}, "mpmath": {"depends": [], "file_name": "mpmath-1.3.0-py3-none-any.whl", "imports": ["mpmath"], "install_dir": "site", "name": "mpmath", "package_type": "package", "sha256": "1ea4ee89672995ab0490528c40b2d82308557e08d3d4d919b34ab8d2485c9eb4", "shared_library": false, "unvendored_tests": true, "version": "1.3.0"}, "mpmath-tests": {"depends": ["mpmath"], "file_name": "mpmath-tests.tar", "imports": [], "install_dir": "site", "name": "mpmath-tests", "package_type": "package", "sha256": "be700e63458f5181bd4b722ddb8aa191bcf23cdf20936abf4c60f6c22daf96b4", "shared_library": false, "unvendored_tests": false, "version": "1.3.0"}, "msgpack": {"depends": [], "file_name": "msgpack-1.0.5-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["msgpack"], "install_dir": "site", "name": "msgpack", "package_type": "package", "sha256": "27a8cb0114bb2a2ed7d0b3e2a6727e7e6f768e8086ef69090eb0adb06d40dafe", "shared_library": false, "unvendored_tests": false, "version": "1.0.5"}, "msgspec": {"depends": [], "file_name": "msgspec-0.18.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["msgspec"], "install_dir": "site", "name": "msgspec", "package_type": "package", "sha256": "861d1ee8dd43030e913b8c8c6d006637a39486ec2f79dc4f5d65b019b7b6b610", "shared_library": false, "unvendored_tests": false, "version": "0.18.4"}, "msprime": {"depends": ["numpy", "newick", "tskit", "demes"], "file_name": "msprime-1.2.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["msprime"], "install_dir": "site", "name": "msprime", "package_type": "package", "sha256": "df81bb0c1a9dca6d6e59a35ccabcd9e66a541d68f654280f5d5652fcddb2a4d8", "shared_library": false, "unvendored_tests": false, "version": "1.2.0"}, "multidict": {"depends": [], "file_name": "multidict-6.0.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["multidict"], "install_dir": "site", "name": "multidict", "package_type": "package", "sha256": "fc5ac571ec6bae9da91e71855968d5d903c11190c4d9a59c828ed1e5dd0b5f16", "shared_library": false, "unvendored_tests": false, "version": "6.0.4"}, "munch": {"depends": ["setuptools", "six"], "file_name": "munch-4.0.0-py2.py3-none-any.whl", "imports": ["munch"], "install_dir": "site", "name": "munch", "package_type": "package", "sha256": "8c80a1f34abd2167f62c559a033351f603b83a4f832e32d75913ac0899bd17fd", "shared_library": false, "unvendored_tests": false, "version": "4.0.0"}, "mypy": {"depends": [], "file_name": "mypy-1.5.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["mypyc", "mypy"], "install_dir": "site", "name": "mypy", "package_type": "package", "sha256": "707ba885bca5857596360796cb08ead018702fa1ab7f3bb9790481b0621cdd81", "shared_library": false, "unvendored_tests": true, "version": "1.5.1"}, "mypy-tests": {"depends": ["mypy"], "file_name": "mypy-tests.tar", "imports": [], "install_dir": "site", "name": "mypy-tests", "package_type": "package", "sha256": "b280684c71fac89dc8bbc350f5c42bf7cfa79463978b9579b66a2c1712f08434", "shared_library": false, "unvendored_tests": false, "version": "1.5.1"}, "netcdf4": {"depends": ["numpy", "packaging", "h5py", "cftime", "certifi"], "file_name": "netCDF4-1.6.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["netCDF4"], "install_dir": "site", "name": "netcdf4", "package_type": "package", "sha256": "1c8fa6697ceaf52afde1aa65ef4e2e07e63fbe299a3ec30bba6abc8363464922", "shared_library": false, "unvendored_tests": false, "version": "1.6.4"}, "networkx": {"depends": ["decorator", "setuptools", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy"], "file_name": "networkx-3.1-py3-none-any.whl", "imports": ["networkx"], "install_dir": "site", "name": "networkx", "package_type": "package", "sha256": "9af74fa17cf8adc0e6829b6a3a6087aee1565dd6bfcd8e839aa08c7e476fd081", "shared_library": false, "unvendored_tests": true, "version": "3.1"}, "networkx-tests": {"depends": ["networkx"], "file_name": "networkx-tests.tar", "imports": [], "install_dir": "site", "name": "networkx-tests", "package_type": "package", "sha256": "029d8512c7fe2b50484102f1c4108cc34dfcd1a3c3076dae59d0b8117a411fde", "shared_library": false, "unvendored_tests": false, "version": "3.1"}, "newick": {"depends": [], "file_name": "newick-1.9.0-py2.py3-none-any.whl", "imports": ["newick"], "install_dir": "site", "name": "newick", "package_type": "package", "sha256": "d8685c0d28c763f5ca06c3b0ce829a4f5542fb110e8aeafeac0e0036407173be", "shared_library": false, "unvendored_tests": false, "version": "1.9.0"}, "nh3": {"depends": [], "file_name": "nh3-0.2.15-cp37-abi3-emscripten_3_1_46_wasm32.whl", "imports": ["nh3"], "install_dir": "site", "name": "nh3", "package_type": "package", "sha256": "0286e4ae925fc2f4ee212a9e9fc4bde67c00e5343b9347496b54e410987c143f", "shared_library": false, "unvendored_tests": false, "version": "0.2.15"}, "nlopt": {"depends": ["numpy"], "file_name": "nlopt-2.7.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["nlopt"], "install_dir": "site", "name": "nlopt", "package_type": "package", "sha256": "fb1313a99e8cc167a56cd9a773767d252f274bfe71ad11e258523fb6103ef205", "shared_library": false, "unvendored_tests": false, "version": "2.7.0"}, "nltk": {"depends": ["regex", "sqlite3"], "file_name": "nltk-3.8.1-py3-none-any.whl", "imports": ["nltk"], "install_dir": "site", "name": "nltk", "package_type": "package", "sha256": "2f4f369a171adb8e176c85f4a11e655dd923c5f8ca6247a894835e8c3895bcc8", "shared_library": false, "unvendored_tests": true, "version": "3.8.1"}, "nltk-tests": {"depends": ["nltk"], "file_name": "nltk-tests.tar", "imports": [], "install_dir": "site", "name": "nltk-tests", "package_type": "package", "sha256": "a819fca86b1164f92bb5c55d6cb950a5f4dd023002fddf9a7eb36a9c2121093a", "shared_library": false, "unvendored_tests": false, "version": "3.8.1"}, "nose": {"depends": ["setuptools"], "file_name": "nose-1.3.7-py3-none-any.whl", "imports": ["nose"], "install_dir": "site", "name": "nose", "package_type": "package", "sha256": "0a840cb02163bf7dd751fa6774bb4f7da8a5145eb00ad2941ed73d58a5d4eaef", "shared_library": false, "unvendored_tests": false, "version": "1.3.7"}, "numcodecs": {"depends": ["numpy", "msgpack"], "file_name": "numcodecs-0.11.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["numcodecs"], "install_dir": "site", "name": "numcodecs", "package_type": "package", "sha256": "f3cd23ee6a6b2517212924efaa4fbeced7ff00e568ce042967b921ec6bd73b5d", "shared_library": false, "unvendored_tests": true, "version": "0.11.0"}, "numcodecs-tests": {"depends": ["numcodecs"], "file_name": "numcodecs-tests.tar", "imports": [], "install_dir": "site", "name": "numcodecs-tests", "package_type": "package", "sha256": "2bf2125578cd68640031ba9f1952d343f4348b18616a89db51a60ae73579b9ef", "shared_library": false, "unvendored_tests": false, "version": "0.11.0"}, "numpy": {"depends": [], "file_name": "numpy-1.26.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["numpy"], "install_dir": "site", "name": "numpy", "package_type": "package", "sha256": "887f7b1ec5e8e0b545b081ae64fdd893f75f8643e069ff6c2585cf20252f6a71", "shared_library": false, "unvendored_tests": true, "version": "1.26.4"}, "numpy-tests": {"depends": ["numpy"], "file_name": "numpy-tests.tar", "imports": [], "install_dir": "site", "name": "numpy-tests", "package_type": "package", "sha256": "d15cf76710b44923caea4ce010ad81014b723a751e86704d897fd2840f8d6090", "shared_library": false, "unvendored_tests": false, "version": "1.26.4"}, "openblas": {"depends": [], "file_name": "openblas-0.3.23.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "openblas", "package_type": "shared_library", "sha256": "fd4867ee63ad4e9c250faa2ade932ebe0513a99da1368ac77b155a116a4aa55e", "shared_library": true, "unvendored_tests": false, "version": "0.3.23"}, "opencv-python": {"depends": ["numpy"], "file_name": "opencv_python-********-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["cv2"], "install_dir": "site", "name": "opencv-python", "package_type": "package", "sha256": "205d22d0b3cd488d036c8e292f89b55d6ea71fbc7311081e8c3555d731e330d6", "shared_library": false, "unvendored_tests": false, "version": "********"}, "openssl": {"depends": [], "file_name": "openssl-1.1.1n.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "openssl", "package_type": "shared_library", "sha256": "d3a7f501968c6c78c1cb3dfd45128fad7538f4f28bf4e70e12ffdcb838a78387", "shared_library": true, "unvendored_tests": false, "version": "1.1.1n"}, "optlang": {"depends": ["sympy", "six", "swiglpk"], "file_name": "optlang-1.7.0-py2.py3-none-any.whl", "imports": ["optlang"], "install_dir": "site", "name": "optlang", "package_type": "package", "sha256": "caf00d3c7def7b6e8e4a5c3f02b8191d3afd49378ed71c80935f459cc2ed00cd", "shared_library": false, "unvendored_tests": true, "version": "1.7.0"}, "optlang-tests": {"depends": ["optlang"], "file_name": "optlang-tests.tar", "imports": [], "install_dir": "site", "name": "optlang-tests", "package_type": "package", "sha256": "be94d63bb82630106482e8a492aa90db10fbbfecaf90d88b6822853b83b8bd39", "shared_library": false, "unvendored_tests": false, "version": "1.7.0"}, "orjson": {"depends": [], "file_name": "orjson-3.9.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "fdd3aacff8789c1e41c00710917e0714c1417728288a78cdb8d7baa7cc735896", "shared_library": false, "unvendored_tests": false, "version": "3.9.2"}, "packaging": {"depends": [], "file_name": "packaging-23.1-py3-none-any.whl", "imports": ["packaging"], "install_dir": "site", "name": "packaging", "package_type": "package", "sha256": "952adfd0fbede825af3ddd33cf288778a247ef83d92de2118d02e7d188e2d917", "shared_library": false, "unvendored_tests": false, "version": "23.1"}, "pandas": {"depends": ["numpy", "python-dateutil", "pytz"], "file_name": "pandas-1.5.3-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pandas"], "install_dir": "site", "name": "pandas", "package_type": "package", "sha256": "15ebfb3055ea2050434ea90b16d8bbc5f0ab62bb13954d42a368530530bc831e", "shared_library": false, "unvendored_tests": true, "version": "1.5.3"}, "pandas-tests": {"depends": ["pandas"], "file_name": "pandas-tests.tar", "imports": [], "install_dir": "site", "name": "pandas-tests", "package_type": "package", "sha256": "a41a1e628fa74445ed58137503d2eba52818d6eabf59188f1f145f7facec9c99", "shared_library": false, "unvendored_tests": false, "version": "1.5.3"}, "parso": {"depends": [], "file_name": "parso-0.8.3-py2.py3-none-any.whl", "imports": ["parso"], "install_dir": "site", "name": "parso", "package_type": "package", "sha256": "2d9de2bb7ab8a91e33c3997eca4c6258b5d3a103bde32768e47def2d3cfe8a07", "shared_library": false, "unvendored_tests": false, "version": "0.8.3"}, "patsy": {"depends": ["numpy", "six"], "file_name": "patsy-0.5.3-py2.py3-none-any.whl", "imports": ["patsy"], "install_dir": "site", "name": "patsy", "package_type": "package", "sha256": "e065eed649e1912a28872bbebe63f1d97f4f1c88f8f860b3fc4879cb1987a0d1", "shared_library": false, "unvendored_tests": true, "version": "0.5.3"}, "patsy-tests": {"depends": ["patsy"], "file_name": "patsy-tests.tar", "imports": [], "install_dir": "site", "name": "patsy-tests", "package_type": "package", "sha256": "6a142f1e5295c34674a242de51023ae6a3241a18ba4d6fd8c45c234d1815a7e7", "shared_library": false, "unvendored_tests": false, "version": "0.5.3"}, "peewee": {"depends": ["sqlite3", "cffi"], "file_name": "peewee-3.16.3-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["peewee"], "install_dir": "site", "name": "peewee", "package_type": "package", "sha256": "0a4161ad956a70df00e4b9ff67d19187672da1da89663aa1e1f532961aa45149", "shared_library": false, "unvendored_tests": true, "version": "3.16.3"}, "peewee-tests": {"depends": ["peewee"], "file_name": "peewee-tests.tar", "imports": [], "install_dir": "site", "name": "peewee-tests", "package_type": "package", "sha256": "5ae2e4cfd7ac3656cf0dfab38e96c160f7a06c03382eeb72948e0f07708a7a1c", "shared_library": false, "unvendored_tests": false, "version": "3.16.3"}, "pillow": {"depends": [], "file_name": "Pillow-10.0.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["PIL"], "install_dir": "site", "name": "Pillow", "package_type": "package", "sha256": "39e1aedeeaf2d4a5325637d156ce4d1227d1268cc1b730cbacf219c0159d8847", "shared_library": false, "unvendored_tests": false, "version": "10.0.0"}, "pillow-heif": {"depends": ["cffi", "pillow", "lib<PERSON><PERSON>"], "file_name": "pillow_heif-0.8.0-cp36-abi3-emscripten_3_1_46_wasm32.whl", "imports": ["pillow_heif"], "install_dir": "site", "name": "pillow_heif", "package_type": "package", "sha256": "e39bbd232f9ef0b6dc1bef9a5ce5a4f143fc89886872e65a743f80df131a1a81", "shared_library": false, "unvendored_tests": false, "version": "0.8.0"}, "pkgconfig": {"depends": [], "file_name": "pkgconfig-1.5.5-py3-none-any.whl", "imports": ["pkgconfig"], "install_dir": "site", "name": "pkgconfig", "package_type": "package", "sha256": "27e74e8c0708b6149a5333ff21b9a95a240b2b3bb354cf06a6d4032c3decba70", "shared_library": false, "unvendored_tests": false, "version": "1.5.5"}, "pluggy": {"depends": [], "file_name": "pluggy-1.2.0-py3-none-any.whl", "imports": ["pluggy"], "install_dir": "site", "name": "pluggy", "package_type": "package", "sha256": "5f159e8f479b29db802867dec7777c37a6a37f01c260e102c353857468f2cf8c", "shared_library": false, "unvendored_tests": false, "version": "1.2.0"}, "protobuf": {"depends": [], "file_name": "protobuf-4.23.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["google"], "install_dir": "site", "name": "protobuf", "package_type": "package", "sha256": "261f5ca35e5c623a9e7f0c511a880ee591232c40165bbce8197715589e8d711b", "shared_library": false, "unvendored_tests": false, "version": "4.23.1"}, "py": {"depends": [], "file_name": "py-1.11.0-py2.py3-none-any.whl", "imports": ["py"], "install_dir": "site", "name": "py", "package_type": "package", "sha256": "6581bfa1c9bd061a038843c3a7ca9a05e3ef0650abeed50c929240b9a787e850", "shared_library": false, "unvendored_tests": false, "version": "1.11.0"}, "pyclipper": {"depends": [], "file_name": "pyclipper-1.3.0.post4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pyclipper"], "install_dir": "site", "name": "pyclipper", "package_type": "package", "sha256": "353625221e6fdadc6d5de0ecaa6c4b58f1db64926406f5147fa2db9a158998a8", "shared_library": false, "unvendored_tests": false, "version": "1.3.0.post4"}, "pycparser": {"depends": [], "file_name": "pycparser-2.21-py2.py3-none-any.whl", "imports": ["pyc<PERSON><PERSON>"], "install_dir": "site", "name": "pyc<PERSON><PERSON>", "package_type": "package", "sha256": "6789a452fdea2cb56716b55e494acab7a1702e758f9d4e5bcb0e05cf5ce6c277", "shared_library": false, "unvendored_tests": false, "version": "2.21"}, "pycryptodome": {"depends": [], "file_name": "pycryptodome-3.18.0-cp35-abi3-emscripten_3_1_46_wasm32.whl", "imports": ["Crypto"], "install_dir": "site", "name": "pycryptodome", "package_type": "package", "sha256": "d8344dfd765f86d4f9542017056a7b2737ecd5cad0e82872ec4fba51d780367f", "shared_library": false, "unvendored_tests": true, "version": "3.18.0"}, "pycryptodome-tests": {"depends": ["pycryptodome"], "file_name": "pycryptodome-tests.tar", "imports": [], "install_dir": "site", "name": "pycryptodome-tests", "package_type": "package", "sha256": "ce16dfb750ecceb563fc34f564fc6fc5a2deeebc6ae1578b1702cd6fe9caa78d", "shared_library": false, "unvendored_tests": false, "version": "3.18.0"}, "pydantic": {"depends": ["typing-extensions"], "file_name": "pydantic-1.10.7-py3-none-any.whl", "imports": ["pydantic"], "install_dir": "site", "name": "pydantic", "package_type": "package", "sha256": "01490a04f31dc472b338b2d05fba49542fa27776a0a2963f7157e8cada6d7300", "shared_library": false, "unvendored_tests": false, "version": "1.10.7"}, "pydecimal": {"depends": [], "file_name": "pydecimal-1.0.0.zip", "imports": ["_pydecimal"], "install_dir": "stdlib", "name": "pydecimal", "package_type": "cpython_module", "sha256": "e4b077e9c956ad6fb7aaac8514a53972d53a7822c7ca76a0631ec4b589a14342", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "pydoc-data": {"depends": [], "file_name": "pydoc_data-1.0.0.zip", "imports": ["pydoc_data"], "install_dir": "stdlib", "name": "pydoc_data", "package_type": "cpython_module", "sha256": "481c405ca8f981e341fd7e258999e81a104fb096a53d4f9fcd9699479b4e66dd", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "pyerfa": {"depends": ["numpy"], "file_name": "pyerfa-*******-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["erfa"], "install_dir": "site", "name": "pyerfa", "package_type": "package", "sha256": "1dafe745e9169d542f77203c06bdbc27c00c77191be443020aed29f8bccbd29c", "shared_library": false, "unvendored_tests": true, "version": "*******"}, "pyerfa-tests": {"depends": ["pyerfa"], "file_name": "pyerfa-tests.tar", "imports": [], "install_dir": "site", "name": "pyerfa-tests", "package_type": "package", "sha256": "16e0b38da94bd118fb0169c4c0637f8cf3c8022c2310dce524a15f70873d37ae", "shared_library": false, "unvendored_tests": false, "version": "*******"}, "pygments": {"depends": [], "file_name": "Pygments-2.16.1-py3-none-any.whl", "imports": ["pygments"], "install_dir": "site", "name": "Pygments", "package_type": "package", "sha256": "74e6cb066deb00f77860d5d9a13b871f066b9ab637a419ff0d44c77ae96291e5", "shared_library": false, "unvendored_tests": false, "version": "2.16.1"}, "pyheif": {"depends": ["cffi"], "file_name": "pyheif-0.7.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["py<PERSON><PERSON>"], "install_dir": "site", "name": "py<PERSON><PERSON>", "package_type": "package", "sha256": "6ff8382a11ccc2c7edf21c3152630e9e3c7fbfe7292c616d170924100d2ebc40", "shared_library": false, "unvendored_tests": false, "version": "0.7.1"}, "pyinstrument": {"depends": [], "file_name": "pyinstrument-4.4.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pyinstrument"], "install_dir": "site", "name": "pyinstrument", "package_type": "package", "sha256": "3918637e76cfe02e9af806d4da655f448c16ecc4a8d842f14b088a827c9a4c88", "shared_library": false, "unvendored_tests": false, "version": "4.4.0"}, "pynacl": {"depends": ["cffi"], "file_name": "PyNaCl-1.5.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["nacl"], "install_dir": "site", "name": "pynacl", "package_type": "package", "sha256": "9352f532f3cf5bd79ea079eda4da2d9135b8e9f27da79669e070532abff86db8", "shared_library": false, "unvendored_tests": false, "version": "1.5.0"}, "pyodide-http": {"depends": [], "file_name": "pyodide_http-0.2.1-py3-none-any.whl", "imports": ["pyodide_http"], "install_dir": "site", "name": "pyodide-http", "package_type": "package", "sha256": "cb364959d8d17de002aa6509f4ac34b36aa2781f851ce097808ad650be63728f", "shared_library": false, "unvendored_tests": false, "version": "0.2.1"}, "pyparsing": {"depends": [], "file_name": "pyparsing-3.1.1-py3-none-any.whl", "imports": ["pyparsing"], "install_dir": "site", "name": "pyparsing", "package_type": "package", "sha256": "910cabffe24dcded89cd62cbaaabc7892c58d21db03f9809e64da751e7cafecc", "shared_library": false, "unvendored_tests": false, "version": "3.1.1"}, "pyproj": {"depends": ["certifi", "sqlite3"], "file_name": "pyproj-3.4.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "4dec942c310f30d5c71261227dfb533e96c47b6103a501815ee0990ce8b318df", "shared_library": false, "unvendored_tests": false, "version": "3.4.1"}, "pyrsistent": {"depends": [], "file_name": "pyrsistent-0.19.3-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["_pyrsistent_version", "pyrsistent"], "install_dir": "site", "name": "pyrsistent", "package_type": "package", "sha256": "15e2ea3651e95f54af1509909bcdd4756f542a5adac660d48df20f2e27801aad", "shared_library": false, "unvendored_tests": false, "version": "0.19.3"}, "pysam": {"depends": [], "file_name": "pysam-0.22.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pysam"], "install_dir": "site", "name": "pysam", "package_type": "package", "sha256": "829fb0e1ce8def5d43718e10842d37aa19148127e19c5bf66160008ebcc02c8f", "shared_library": false, "unvendored_tests": false, "version": "0.22.0"}, "pyshp": {"depends": [], "file_name": "pyshp-2.3.1-py2.py3-none-any.whl", "imports": ["shapefile"], "install_dir": "site", "name": "pyshp", "package_type": "package", "sha256": "5a0b246d4fa589d1de7c29abe8931c5697ac5703aa2b54d784c54b6ff5d46148", "shared_library": false, "unvendored_tests": false, "version": "2.3.1"}, "pytest": {"depends": ["atomicwrites", "attrs", "more-itertools", "pluggy", "py", "setuptools", "six", "iniconfig", "exceptiongroup"], "file_name": "pytest-7.2.2-py3-none-any.whl", "imports": ["_pytest", "pytest"], "install_dir": "site", "name": "pytest", "package_type": "package", "sha256": "37cda8b095c1b775933b68571d52ea98203e16234870f6c34b690da86674ff32", "shared_library": false, "unvendored_tests": false, "version": "7.2.2"}, "pytest-benchmark": {"depends": [], "file_name": "pytest_benchmark-4.0.0-py3-none-any.whl", "imports": ["pytest_benchmark"], "install_dir": "site", "name": "pytest-benchmark", "package_type": "package", "sha256": "d0d142386ce39aa40029ce8143dc271733341aaad74af98b80b5f28278be65a4", "shared_library": false, "unvendored_tests": false, "version": "4.0.0"}, "python-dateutil": {"depends": ["six"], "file_name": "python_dateutil-2.8.2-py2.py3-none-any.whl", "imports": ["dateutil"], "install_dir": "site", "name": "python-dateutil", "package_type": "package", "sha256": "a7b0404fca1ccf0a0bbdf8398f1f54433e21f4cd3d033408a744de5d0d18826f", "shared_library": false, "unvendored_tests": false, "version": "2.8.2"}, "python-magic": {"depends": ["libmagic"], "file_name": "python_magic-0.4.27-py2.py3-none-any.whl", "imports": ["magic"], "install_dir": "site", "name": "python-magic", "package_type": "package", "sha256": "81a716761a01d93a128e26d070c53a29244ed2eadb4ec0db1a6e9f42381efcff", "shared_library": false, "unvendored_tests": false, "version": "0.4.27"}, "python-sat": {"depends": ["six"], "file_name": "python_sat-0.1.7.dev26-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pysat"], "install_dir": "site", "name": "python-sat", "package_type": "package", "sha256": "4d9d765ceca41194a53b802c059f1b52eda6f9845e04761a5f8d436955c6219f", "shared_library": false, "unvendored_tests": false, "version": "0.1.7.dev26"}, "python-solvespace": {"depends": [], "file_name": "python_solvespace-3.0.8-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["python_solvespace"], "install_dir": "site", "name": "python_solvespace", "package_type": "package", "sha256": "3c3611abebb8988917674c143e21734861faa635ada202ec7426793262dd3339", "shared_library": false, "unvendored_tests": false, "version": "3.0.8"}, "pytz": {"depends": [], "file_name": "pytz-2023.3-py2.py3-none-any.whl", "imports": ["pytz"], "install_dir": "site", "name": "pytz", "package_type": "package", "sha256": "9642fe77a66578d7f22585f9299eb41ef8aee3791114bcd02ca1f5108ecb5d8f", "shared_library": false, "unvendored_tests": false, "version": "2023.3"}, "pywavelets": {"depends": ["distutils", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy"], "file_name": "PyWavelets-1.4.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["pywt"], "install_dir": "site", "name": "pywavelets", "package_type": "package", "sha256": "6f0831bdf94d12da7b1cf60e1f8cc2fecb1edf65865253da5758ff2192c08335", "shared_library": false, "unvendored_tests": true, "version": "1.4.1"}, "pywavelets-tests": {"depends": ["pywavelets"], "file_name": "pywavelets-tests.tar", "imports": [], "install_dir": "site", "name": "pywavelets-tests", "package_type": "package", "sha256": "8370c9ea1e4039f2f24447aeeeb6b4aadf6aeee5bbf409997cbf188922354bba", "shared_library": false, "unvendored_tests": false, "version": "1.4.1"}, "pyxel": {"depends": [], "file_name": "pyxel-1.9.10-cp37-abi3-emscripten_3_1_46_wasm32.whl", "imports": ["pyxel"], "install_dir": "site", "name": "pyxel", "package_type": "package", "sha256": "5728101895e2c9dd5b70b5e1ee893ff4a2db9fdd306273c86808ed85b4bae7ef", "shared_library": false, "unvendored_tests": false, "version": "1.9.10"}, "pyyaml": {"depends": [], "file_name": "PyYAML-6.0.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["_yaml", "yaml"], "install_dir": "site", "name": "pyyaml", "package_type": "package", "sha256": "70da5e4d3c7b6cf4befce24f971d94c21fc8a41e28daf34b2da0f0d99ef0b565", "shared_library": false, "unvendored_tests": false, "version": "6.0.1"}, "rebound": {"depends": ["numpy"], "file_name": "rebound-3.24.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["rebound"], "install_dir": "site", "name": "rebound", "package_type": "package", "sha256": "f8dc7d482a31ca87355d8ae849783f4aaf61daa984474a0abeb15a2cad498e04", "shared_library": false, "unvendored_tests": false, "version": "3.24.2"}, "reboundx": {"depends": ["rebound", "numpy"], "file_name": "reboundx-3.10.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["reboundx"], "install_dir": "site", "name": "reboundx", "package_type": "package", "sha256": "9baf0fc2873918f4555300a1003b811f9dfc8704e8bccd4457707c5d87f6f288", "shared_library": false, "unvendored_tests": false, "version": "3.10.1"}, "regex": {"depends": [], "file_name": "regex-2023.8.8-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["regex"], "install_dir": "site", "name": "regex", "package_type": "package", "sha256": "a62b3caad35d144ce4fa499ad39657e031d8803df25cd790d762032cb7656ff8", "shared_library": false, "unvendored_tests": true, "version": "2023.8.8"}, "regex-tests": {"depends": ["regex"], "file_name": "regex-tests.tar", "imports": [], "install_dir": "site", "name": "regex-tests", "package_type": "package", "sha256": "44431a424879b1ca4ea89b9de02dd1dccbfe77ff507598ddc638b8a8860737eb", "shared_library": false, "unvendored_tests": false, "version": "2023.8.8"}, "requests": {"depends": ["charset-normalizer", "idna", "urllib3", "certifi"], "file_name": "requests-2.31.0-py3-none-any.whl", "imports": ["requests"], "install_dir": "site", "name": "requests", "package_type": "package", "sha256": "98a4d2e9c220884a6e17b1b543b7178c957f3ebb2840ffb0fb214b8bac3f32dc", "shared_library": false, "unvendored_tests": false, "version": "2.31.0"}, "retrying": {"depends": ["six"], "file_name": "retrying-1.3.4-py3-none-any.whl", "imports": ["retrying"], "install_dir": "site", "name": "retrying", "package_type": "package", "sha256": "19e2898888825a8c3a59dba5624b1c74d6b1f0bf28533ed05a0993a973c544cf", "shared_library": false, "unvendored_tests": false, "version": "1.3.4"}, "river": {"depends": ["numpy", "pandas", "pytest", "scipy"], "file_name": "river-0.19.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["river"], "install_dir": "site", "name": "river", "package_type": "package", "sha256": "2caf2bf8b027f4a42fd0a9d8b15066c85c0caf23d363f0768cc4cb583c50afd2", "shared_library": false, "unvendored_tests": true, "version": "0.19.0"}, "river-tests": {"depends": ["river"], "file_name": "river-tests.tar", "imports": [], "install_dir": "site", "name": "river-tests", "package_type": "package", "sha256": "2859ccd4355aa0a9779ce74407f3717daecc512fd95c374ca8869c8e595a230c", "shared_library": false, "unvendored_tests": false, "version": "0.19.0"}, "robotraconteur": {"depends": ["numpy"], "file_name": "RobotRaconteur-0.15.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["RobotRaconteur"], "install_dir": "site", "name": "RobotRaconteur", "package_type": "package", "sha256": "842f69e897a47cb2837b0e099bae837117dd9a771c71dc239e99130d801b2678", "shared_library": false, "unvendored_tests": false, "version": "0.15.1"}, "ruamel-yaml": {"depends": [], "file_name": "ruamel.yaml-0.17.32-py3-none-any.whl", "imports": ["rua<PERSON>"], "install_dir": "site", "name": "ruamel.yaml", "package_type": "package", "sha256": "e81940c2003cb6bb53b396f6d553fcccae55a813ad399aa50cf08aa6a874c8fe", "shared_library": false, "unvendored_tests": false, "version": "0.17.32"}, "rust-panic-test": {"depends": [], "file_name": "rust_panic_test-1.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["rust-panic-test"], "install_dir": "site", "name": "rust-panic-test", "package_type": "package", "sha256": "f37e7a9f92a3f82c02178679a014cd9f305465d9fc935a9e2115980d46f5a987", "shared_library": false, "unvendored_tests": false, "version": "1.0"}, "scikit-image": {"depends": ["packaging", "numpy", "scipy", "networkx", "pillow", "imageio", "pywavelets", "lazy_loader"], "file_name": "scikit_image-0.21.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["skimage"], "install_dir": "site", "name": "scikit-image", "package_type": "package", "sha256": "915d57c321b7dbb49adfa70b500725b6a17ebf4ef47c60a4229dcfdcb9c51bc0", "shared_library": false, "unvendored_tests": true, "version": "0.21.0"}, "scikit-image-tests": {"depends": ["scikit-image"], "file_name": "scikit-image-tests.tar", "imports": [], "install_dir": "site", "name": "scikit-image-tests", "package_type": "package", "sha256": "673dc4ad087499925acfaea2b9ad833b31ced37e512a5dfb90a293ee78e14c95", "shared_library": false, "unvendored_tests": false, "version": "0.21.0"}, "scikit-learn": {"depends": ["scipy", "joblib", "threadpoolctl"], "file_name": "scikit_learn-1.3.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["sklearn"], "install_dir": "site", "name": "scikit-learn", "package_type": "package", "sha256": "7799c5b0cd493b05ad14fb23b432b77163dc09b19e151b07b9006a05d6ed1d75", "shared_library": false, "unvendored_tests": true, "version": "1.3.1"}, "scikit-learn-tests": {"depends": ["scikit-learn"], "file_name": "scikit-learn-tests.tar", "imports": [], "install_dir": "site", "name": "scikit-learn-tests", "package_type": "package", "sha256": "de9494dd22b7c8b5fa412239a53a16c1076a578c274f19b2d8e7ca9d299f815a", "shared_library": false, "unvendored_tests": false, "version": "1.3.1"}, "scipy": {"depends": ["numpy", "openblas"], "file_name": "scipy-1.11.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["scipy"], "install_dir": "site", "name": "scipy", "package_type": "package", "sha256": "20fb3193b0444614045692c6f3bddfe33dea155d7b2797c779f4079fe0f4fb86", "shared_library": false, "unvendored_tests": true, "version": "1.11.2"}, "scipy-tests": {"depends": ["scipy"], "file_name": "scipy-tests.tar", "imports": [], "install_dir": "site", "name": "scipy-tests", "package_type": "package", "sha256": "9a1276ae38f604e6f80c6ed4153fb56f1f9b0955da940c20b23715b0c259f60a", "shared_library": false, "unvendored_tests": false, "version": "1.11.2"}, "screed": {"depends": [], "file_name": "screed-1.1.2-py2.py3-none-any.whl", "imports": ["bigtests", "screed"], "install_dir": "site", "name": "screed", "package_type": "package", "sha256": "0c0e76342354c4dccbb458fec75ff682793f866802b06f019ecf0a09e0d6b336", "shared_library": false, "unvendored_tests": true, "version": "1.1.2"}, "screed-tests": {"depends": ["screed"], "file_name": "screed-tests.tar", "imports": [], "install_dir": "site", "name": "screed-tests", "package_type": "package", "sha256": "0625705f10451327ea5b33c20a1725383a4fa1190d84794df0be5b4f781eddcc", "shared_library": false, "unvendored_tests": false, "version": "1.1.2"}, "setuptools": {"depends": ["distutils", "pyparsing"], "file_name": "setuptools-68.1.2-py3-none-any.whl", "imports": ["_distutils_hack", "pkg_resources", "setuptools"], "install_dir": "site", "name": "setuptools", "package_type": "package", "sha256": "7d0c185c16974f0f57f848fcc16c2647dce1a78b7c5f124400716ec2a2053ce1", "shared_library": false, "unvendored_tests": false, "version": "68.1.2"}, "shapely": {"depends": ["numpy"], "file_name": "Shapely-1.8.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["shapely"], "install_dir": "site", "name": "shapely", "package_type": "package", "sha256": "f0d41e050143716feaa5b63003422969f9b2e37bb23dd4db0995f47e8ce45465", "shared_library": false, "unvendored_tests": true, "version": "1.8.2"}, "shapely-tests": {"depends": ["shapely"], "file_name": "shapely-tests.tar", "imports": [], "install_dir": "site", "name": "shapely-tests", "package_type": "package", "sha256": "c5edffecf747de727e8b12cf1c034fbc74e9081c7c577f34087dcd7bc6058973", "shared_library": false, "unvendored_tests": false, "version": "1.8.2"}, "sharedlib-test": {"depends": [], "file_name": "sharedlib-test-1.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "sharedlib-test", "package_type": "shared_library", "sha256": "553ac8833e45246c88960b71659915fc2b1263404228d01692193abd2d4ab78f", "shared_library": true, "unvendored_tests": false, "version": "1.0"}, "sharedlib-test-py": {"depends": ["sharedlib-test"], "file_name": "sharedlib_test_py-1.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["sharedlib_test"], "install_dir": "site", "name": "sharedlib-test-py", "package_type": "package", "sha256": "9038ae4d3727e8c57ba233888ad0f0d45a55285a9d7f0ba20bf88fc40579902e", "shared_library": false, "unvendored_tests": false, "version": "1.0"}, "simplejson": {"depends": [], "file_name": "simplejson-3.19.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "a449dd8dc4143369d0cc1803c301b101f785980bc1868821edf4f74730112bb9", "shared_library": false, "unvendored_tests": true, "version": "3.19.1"}, "simplejson-tests": {"depends": ["<PERSON><PERSON><PERSON>"], "file_name": "simplejson-tests.tar", "imports": [], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>-tests", "package_type": "package", "sha256": "f952a666496903cda9258ab6dbc4f7b6eca1c37bb27d1fca75db1af3afd0a7d8", "shared_library": false, "unvendored_tests": false, "version": "3.19.1"}, "sisl": {"depends": ["pyparsing", "numpy", "scipy", "tqdm", "xarray", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "sisl-0.14.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["sisl_toolbox", "sisl"], "install_dir": "site", "name": "sisl", "package_type": "package", "sha256": "add61e40a5308fd97a2ee4e09be502ec03bef138927bc87119551d62dae66f76", "shared_library": false, "unvendored_tests": true, "version": "0.14.2"}, "sisl-tests": {"depends": ["sisl"], "file_name": "sisl-tests.tar", "imports": [], "install_dir": "site", "name": "sisl-tests", "package_type": "package", "sha256": "41c47ca42bec2114e222b4ead743f1557ade03ae1fff3be0f2cfd467ced5f9c7", "shared_library": false, "unvendored_tests": false, "version": "0.14.2"}, "six": {"depends": [], "file_name": "six-1.16.0-py2.py3-none-any.whl", "imports": ["six"], "install_dir": "site", "name": "six", "package_type": "package", "sha256": "fad96a9848b9ddc039f72d0e07fee1d4a7caf15c3e4beda9a114fd4875c52583", "shared_library": false, "unvendored_tests": false, "version": "1.16.0"}, "smart-open": {"depends": [], "file_name": "smart_open-6.3.0-py3-none-any.whl", "imports": ["smart_open"], "install_dir": "site", "name": "smart_open", "package_type": "package", "sha256": "b4059b9b7c6185620e71d0562fabb83ed66bbc3c37ec94676c90185a4cbb80c1", "shared_library": false, "unvendored_tests": false, "version": "6.3.0"}, "soupsieve": {"depends": [], "file_name": "soupsieve-2.4.1-py3-none-any.whl", "imports": ["soupsieve"], "install_dir": "site", "name": "soupsieve", "package_type": "package", "sha256": "31d65e0030897b0d7251ad328e200881f9fe5416891addb1b9a33a77d127f112", "shared_library": false, "unvendored_tests": false, "version": "2.4.1"}, "sourmash": {"depends": ["screed", "cffi", "deprecation", "cachetools", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy", "sqlite3", "bitstring"], "file_name": "sourmash-4.8.4-py3-none-emscripten_3_1_46_wasm32.whl", "imports": ["sourmash"], "install_dir": "site", "name": "sourmash", "package_type": "package", "sha256": "8d8fbdb14218be1b592214799d8a437ccf7a3f6598c474e2946f2a5d9041ae44", "shared_library": false, "unvendored_tests": false, "version": "4.8.4"}, "sparseqr": {"depends": ["pyc<PERSON><PERSON>", "cffi", "numpy", "scipy", "suitesparse", "distutils"], "file_name": "sparseqr-1.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["sparseqr"], "install_dir": "site", "name": "sparseqr", "package_type": "package", "sha256": "c8eccd6783c393126e4239408dd315084a4e254d69627fbec98b455f3b0bc7e6", "shared_library": false, "unvendored_tests": false, "version": "1.2"}, "sqlalchemy": {"depends": ["sqlite3", "typing-extensions"], "file_name": "SQLAlchemy-2.0.20-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["sqlalchemy"], "install_dir": "site", "name": "sqlalchemy", "package_type": "package", "sha256": "58c78a8d513bc37d5e974759ba07f5369581d240c02178d380965c8a96a682f2", "shared_library": false, "unvendored_tests": true, "version": "2.0.20"}, "sqlalchemy-tests": {"depends": ["sqlalchemy"], "file_name": "sqlalchemy-tests.tar", "imports": [], "install_dir": "site", "name": "sqlalchemy-tests", "package_type": "package", "sha256": "4263699b03d8f34ec402b0cd2126b6082c9eb58ca81a05a705319356be5b2e8f", "shared_library": false, "unvendored_tests": false, "version": "2.0.20"}, "sqlite3": {"depends": [], "file_name": "sqlite3-1.0.0.zip", "imports": ["sqlite3", "_sqlite3"], "install_dir": "stdlib", "name": "sqlite3", "package_type": "cpython_module", "sha256": "49dad33820f94c406be5b8fd06f618b16824818476d96e35e5faef6a319f8835", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "ssl": {"depends": ["openssl"], "file_name": "ssl-1.0.0.zip", "imports": ["ssl", "_ssl"], "install_dir": "stdlib", "name": "ssl", "package_type": "cpython_module", "sha256": "89b210fdbd9f25173d0477b10613d0c7ebd662e2c26fcabbe6959c4a83197573", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "statsmodels": {"depends": ["distutils", "numpy", "scipy", "pandas", "patsy", "packaging"], "file_name": "statsmodels-0.14.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["statsmodels"], "install_dir": "site", "name": "statsmodels", "package_type": "package", "sha256": "18e28f46dc960d4dc04e12bc67fca32f9778ca0b756ba7b25486bae7c6df4861", "shared_library": false, "unvendored_tests": true, "version": "0.14.0"}, "statsmodels-tests": {"depends": ["statsmodels"], "file_name": "statsmodels-tests.tar", "imports": [], "install_dir": "site", "name": "statsmodels-tests", "package_type": "package", "sha256": "24c2204b8f5b15f5677cf04edb2cc9a6938d76cbe6adfc53bdf0b74a2aaf3ec7", "shared_library": false, "unvendored_tests": false, "version": "0.14.0"}, "suitesparse": {"depends": ["openblas"], "file_name": "suitesparse-5.11.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "suitesparse", "package_type": "shared_library", "sha256": "2f146944f72146cdf700e2c79a94321957c235ec1c63bd040413d322baf64ffb", "shared_library": true, "unvendored_tests": false, "version": "5.11.0"}, "svgwrite": {"depends": [], "file_name": "svgwrite-1.4.3-py3-none-any.whl", "imports": ["svgwrite"], "install_dir": "site", "name": "svgwrite", "package_type": "package", "sha256": "2e01c175913b8d1abdadf81671321f5b8c00c5574f6884e031677aaebf3b0011", "shared_library": false, "unvendored_tests": false, "version": "1.4.3"}, "swiglpk": {"depends": [], "file_name": "swiglpk-5.0.8-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["swiglpk"], "install_dir": "site", "name": "swiglpk", "package_type": "package", "sha256": "9c0951b2ab9d3996bfb9eca346c49b71065f448c33ad970a28afbcab79fe9eef", "shared_library": false, "unvendored_tests": false, "version": "5.0.8"}, "sympy": {"depends": ["distutils", "mpmath"], "file_name": "sympy-1.12-py3-none-any.whl", "imports": ["isympy", "sympy"], "install_dir": "site", "name": "sympy", "package_type": "package", "sha256": "b7075c6d69bffda1afc6eacdbbebc7d82437508539e855f34f01c4c6dc9137b6", "shared_library": false, "unvendored_tests": true, "version": "1.12"}, "sympy-tests": {"depends": ["sympy"], "file_name": "sympy-tests.tar", "imports": [], "install_dir": "site", "name": "sympy-tests", "package_type": "package", "sha256": "b5811c381240f14d2d7edbd481325c1c3491866847032491606c3c36f1172f86", "shared_library": false, "unvendored_tests": false, "version": "1.12"}, "tblib": {"depends": [], "file_name": "tblib-3.0.0-py3-none-any.whl", "imports": ["tblib"], "install_dir": "site", "name": "tblib", "package_type": "package", "sha256": "091820a33875a03e3e12ce3050ed8a147940f9bd56840a42c2f6cba0c14e77fa", "shared_library": false, "unvendored_tests": false, "version": "3.0.0"}, "termcolor": {"depends": [], "file_name": "termcolor-2.3.0-py3-none-any.whl", "imports": ["termcolor"], "install_dir": "site", "name": "termcolor", "package_type": "package", "sha256": "8481f308073f0c8b2b551df71bab8f7df2d733c0f22934ea772b37fec0e49dfc", "shared_library": false, "unvendored_tests": false, "version": "2.3.0"}, "test": {"depends": [], "file_name": "test-1.0.0.zip", "imports": ["test"], "install_dir": "stdlib", "name": "test", "package_type": "cpython_module", "sha256": "00233e7098ca725070b62a0575ef0e4414f3b2f978c2601bcde2a325a5c672b6", "shared_library": true, "unvendored_tests": false, "version": "1.0.0"}, "texttable": {"depends": [], "file_name": "texttable-1.6.7-py2.py3-none-any.whl", "imports": ["texttable"], "install_dir": "site", "name": "texttable", "package_type": "package", "sha256": "bfd20ec95d90865c2c0819c38dde9535588e93da45ee0f866004d17d3ca10451", "shared_library": false, "unvendored_tests": false, "version": "1.6.7"}, "threadpoolctl": {"depends": [], "file_name": "threadpoolctl-3.2.0-py3-none-any.whl", "imports": ["threadpoolctl"], "install_dir": "site", "name": "threadpoolctl", "package_type": "package", "sha256": "73fa129cbab9daa4a0f381af17824a932b8957777a9e73a02bf733db9040ed7a", "shared_library": false, "unvendored_tests": false, "version": "3.2.0"}, "tomli": {"depends": [], "file_name": "tomli-2.0.1-py3-none-any.whl", "imports": ["to<PERSON>li"], "install_dir": "site", "name": "to<PERSON>li", "package_type": "package", "sha256": "86c6b2919638edcca8ce1762e4e70a95553492dae67fbce61205d154aa7ec7d7", "shared_library": false, "unvendored_tests": false, "version": "2.0.1"}, "tomli-w": {"depends": [], "file_name": "tomli_w-1.0.0-py3-none-any.whl", "imports": ["tomli_w"], "install_dir": "site", "name": "tomli-w", "package_type": "package", "sha256": "a7c41f6e333c3554e96d895ec1b1500d4cf05039786106ae54330cbf0b2cffed", "shared_library": false, "unvendored_tests": false, "version": "1.0.0"}, "toolz": {"depends": [], "file_name": "toolz-0.12.0-py3-none-any.whl", "imports": ["tlz", "toolz"], "install_dir": "site", "name": "toolz", "package_type": "package", "sha256": "5f2ed674746b0564f59ca4d89cec69253a03f55b6233e10a7fe3918cc712450b", "shared_library": false, "unvendored_tests": true, "version": "0.12.0"}, "toolz-tests": {"depends": ["toolz"], "file_name": "toolz-tests.tar", "imports": [], "install_dir": "site", "name": "toolz-tests", "package_type": "package", "sha256": "4499b52adfde700758f72039149c3032f51f3eef0093d56870de16281b96054f", "shared_library": false, "unvendored_tests": false, "version": "0.12.0"}, "tqdm": {"depends": [], "file_name": "tqdm-4.66.1-py3-none-any.whl", "imports": ["tqdm"], "install_dir": "site", "name": "tqdm", "package_type": "package", "sha256": "81502627ac31f06a64d73833d1303cd530e299125c6ce81b40e7a4115dd57314", "shared_library": false, "unvendored_tests": false, "version": "4.66.1"}, "traits": {"depends": [], "file_name": "traits-6.4.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["traits"], "install_dir": "site", "name": "traits", "package_type": "package", "sha256": "731ae94a327f4ec6216fd2cc69e074468dd08360fcf73c93be2e1186ed107e1c", "shared_library": false, "unvendored_tests": true, "version": "6.4.2"}, "traits-tests": {"depends": ["traits"], "file_name": "traits-tests.tar", "imports": [], "install_dir": "site", "name": "traits-tests", "package_type": "package", "sha256": "2da74f37e577489e6d4c0565fe2353e6e3601937820218d4785c8a14180fccd0", "shared_library": false, "unvendored_tests": false, "version": "6.4.2"}, "tskit": {"depends": ["numpy", "svgwrite", "jsonschema"], "file_name": "tskit-0.5.5-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["tskit"], "install_dir": "site", "name": "tskit", "package_type": "package", "sha256": "df70c2756ac1810c14715fca06be0a59576627b2a33b9ec5d730e06450c6298a", "shared_library": false, "unvendored_tests": false, "version": "0.5.5"}, "typing-extensions": {"depends": [], "file_name": "typing_extensions-4.7.1-py3-none-any.whl", "imports": ["typing_extensions"], "install_dir": "site", "name": "typing-extensions", "package_type": "package", "sha256": "eb21dbb2a3bcaba4e0f6d2345906fb7635ac6f8287a0133ebef35cb81489e668", "shared_library": false, "unvendored_tests": false, "version": "4.7.1"}, "uncertainties": {"depends": ["future"], "file_name": "uncertainties-3.1.7-py2.py3-none-any.whl", "imports": ["uncertainties"], "install_dir": "site", "name": "uncertainties", "package_type": "package", "sha256": "23cb91fc29fc75c1a34ffc8273a38fa1e8033da451461eae889628217f14f2c8", "shared_library": false, "unvendored_tests": true, "version": "3.1.7"}, "uncertainties-tests": {"depends": ["uncertainties"], "file_name": "uncertainties-tests.tar", "imports": [], "install_dir": "site", "name": "uncertainties-tests", "package_type": "package", "sha256": "b85cf522aa76b86cbcafb243fb1a29b27c865ad85107420bca6afc8914ca4b1b", "shared_library": false, "unvendored_tests": false, "version": "3.1.7"}, "unyt": {"depends": ["numpy", "sympy"], "file_name": "unyt-2.9.5-py2.py3-none-any.whl", "imports": ["unyt"], "install_dir": "site", "name": "unyt", "package_type": "package", "sha256": "cb49e4927dae619a9e43659e4a18a7868aa3f82656d34ce1c18b0cc0b8e03181", "shared_library": false, "unvendored_tests": true, "version": "2.9.5"}, "unyt-tests": {"depends": ["unyt"], "file_name": "unyt-tests.tar", "imports": [], "install_dir": "site", "name": "unyt-tests", "package_type": "package", "sha256": "bbf8a9b09e2762167dd80428fb1dc95830cadc57196cd09d134bc1055e0a4b3d", "shared_library": false, "unvendored_tests": false, "version": "2.9.5"}, "urllib3": {"depends": [], "file_name": "urllib3-2.1.0-py3-none-any.whl", "imports": ["urllib3"], "install_dir": "site", "name": "urllib3", "package_type": "package", "sha256": "0dc9140041c8ba0786e7690df6e466ebee96e726469d07bb9552abd8ff653a6d", "shared_library": false, "unvendored_tests": false, "version": "2.1.0"}, "webencodings": {"depends": [], "file_name": "webencodings-0.5.1-py2.py3-none-any.whl", "imports": ["webencodings"], "install_dir": "site", "name": "webencodings", "package_type": "package", "sha256": "973e83c0220cdedf3ab39288da0a60f97d047abe8f481a30c936856413d6de60", "shared_library": false, "unvendored_tests": false, "version": "0.5.1"}, "wordcloud": {"depends": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "wordcloud-1.9.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["wordcloud"], "install_dir": "site", "name": "wordcloud", "package_type": "package", "sha256": "8d4cc4fc724f2d4f809c9c31050a591e0e5ef51c86eda4b3c6f6a3e0103e1506", "shared_library": false, "unvendored_tests": false, "version": "1.9.2"}, "wrapt": {"depends": [], "file_name": "wrapt-1.15.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["wrapt"], "install_dir": "site", "name": "wrapt", "package_type": "package", "sha256": "cfe5c594af872251a992c13cd7c9a1d9438c0a11da5c470eef7d7713773794e3", "shared_library": false, "unvendored_tests": false, "version": "1.15.0"}, "xarray": {"depends": ["numpy", "packaging", "pandas"], "file_name": "xarray-2023.12.0-py3-none-any.whl", "imports": ["xarray"], "install_dir": "site", "name": "xarray", "package_type": "package", "sha256": "6d201070f094dfbbcfad9a4b2b6cd390d786c6cc21b7be926ba925a0a1fb3113", "shared_library": false, "unvendored_tests": true, "version": "2023.12.0"}, "xarray-tests": {"depends": ["xarray"], "file_name": "xarray-tests.tar", "imports": [], "install_dir": "site", "name": "xarray-tests", "package_type": "package", "sha256": "f6add9e859a0951f37cb2972d51f4f59ca7b2ae5baa58609dbc6dfa2f53e7e9f", "shared_library": false, "unvendored_tests": false, "version": "2023.12.0"}, "xgboost": {"depends": ["numpy", "scipy", "setuptools"], "file_name": "xgboost-1.6.1-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["xgboost"], "install_dir": "site", "name": "xgboost", "package_type": "package", "sha256": "6a1838cb28ffafb570a582f8d1f5f7905c8419f504f0b323a54a81ebcaf56089", "shared_library": false, "unvendored_tests": false, "version": "1.6.1"}, "xlrd": {"depends": [], "file_name": "xlrd-2.0.1-py2.py3-none-any.whl", "imports": ["xlrd"], "install_dir": "site", "name": "xlrd", "package_type": "package", "sha256": "5f4ee0aadb7f0364a27454e47bf8ba0a8848a6fbc976209476cf50d9bbdbb823", "shared_library": false, "unvendored_tests": false, "version": "2.0.1"}, "xyzservices": {"depends": [], "file_name": "xyzservices-2023.7.0-py3-none-any.whl", "imports": ["xyzservices"], "install_dir": "site", "name": "xyzservices", "package_type": "package", "sha256": "b20b3b2c33dc4670664d2b00f5b925eedb72b6dddb6f74098f5e1bf29653fb8e", "shared_library": false, "unvendored_tests": true, "version": "2023.7.0"}, "xyzservices-tests": {"depends": ["xyzservices"], "file_name": "xyzservices-tests.tar", "imports": [], "install_dir": "site", "name": "xyzservices-tests", "package_type": "package", "sha256": "4314b2d64d1185b8087e9bad1b9a3f2a54a7c12be957ce56baea336dbf27b19a", "shared_library": false, "unvendored_tests": false, "version": "2023.7.0"}, "yarl": {"depends": ["multidict", "idna"], "file_name": "yarl-1.9.2-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["yarl"], "install_dir": "site", "name": "yarl", "package_type": "package", "sha256": "f009548bcd61530020e293e44bc71f2094b964076738d747383b81127aad313b", "shared_library": false, "unvendored_tests": false, "version": "1.9.2"}, "yt": {"depends": ["numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sympy", "setuptools", "packaging", "unyt", "cmyt", "colorspacious", "tqdm", "to<PERSON>li", "tomli-w"], "file_name": "yt-4.1.4-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["yt"], "install_dir": "site", "name": "yt", "package_type": "package", "sha256": "6ab5ca825fe792a92667d373cfa8c7e3ebf801b91d61942d97e79b2ec130632c", "shared_library": false, "unvendored_tests": false, "version": "4.1.4"}, "zarr": {"depends": ["numpy", "as<PERSON><PERSON><PERSON>", "numcodecs"], "file_name": "zarr-2.13.3-py3-none-any.whl", "imports": ["zarr"], "install_dir": "site", "name": "zarr", "package_type": "package", "sha256": "2637c668cca2ec8a295256b2540e913414f6119c61a2190c6e4d53f20a74c3ea", "shared_library": false, "unvendored_tests": true, "version": "2.13.3"}, "zarr-tests": {"depends": ["zarr"], "file_name": "zarr-tests.tar", "imports": [], "install_dir": "site", "name": "zarr-tests", "package_type": "package", "sha256": "d411b4e0b526ca7cfc953fc2081836b0dbd32a7728be266f4fe0d35285cb6c06", "shared_library": false, "unvendored_tests": false, "version": "2.13.3"}, "zengl": {"depends": [], "file_name": "zengl-2.2.0-cp311-cp311-emscripten_3_1_46_wasm32.whl", "imports": ["zengl", "_zengl", "_zengl_js"], "install_dir": "site", "name": "zengl", "package_type": "package", "sha256": "da5d34f3591f9d745f907b95298cb758761f1e109e061d7adaf21d970f591c9a", "shared_library": false, "unvendored_tests": false, "version": "2.2.0"}}}