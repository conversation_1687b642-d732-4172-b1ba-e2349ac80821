// Refactored conversion functions
const hexStrToU8 = hexString => Uint8Array.from(hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));

const u8ToHexStr = bytes => bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');

const b64ToHexStr = b64 => [...atob(b64)].map(c => c.charCodeAt(0).toString(16).padStart(2, 0)).join``

// initData to PSSH
function getPssh(buffer) {
    const bytes = hexStrToU8(u8ToHexStr(new Uint8Array(buffer)).match(/000000..70737368.*/)[0]);
    return window.btoa(String.fromCharCode(...bytes));
}

// Get Clearkey keys
function getClearkey(response) {
    let obj = JSON.parse((new TextDecoder("utf-8")).decode(response))
    obj = obj["keys"].map(o => [o["kid"], o["k"]]);
    obj = obj.map(o => o.map(a => a.replace(/-/g, '+').replace(/_/g, '/') + "=="))
    return obj.map(o => `${b64ToHexStr(o[0])}:${b64ToHexStr(o[1])}`).join("\n")

}

// Widevine PSSH extraction from init
const originalGenerateRequest = MediaKeySession.prototype.generateRequest;
MediaKeySession.prototype.generateRequest = function (initDataType, initData) {
    const result = originalGenerateRequest.call(this, initDataType, initData);
    //Get PSSH and pass into content.js
    try {
        console.log("[PSSH] " + getPssh(initData))
        document.dispatchEvent(new CustomEvent('pssh', {
            detail: getPssh(initData)
        }));
    } finally {
        return result;
    }
};

//Clearkey Support
const originalUpdate = MediaKeySession.prototype.update;
MediaKeySession.prototype.update = function (response) {
    const result = originalUpdate.call(this, response);
    try {
        console.log("[CLEARKEY] " + getClearkey(response));
        document.dispatchEvent(new CustomEvent('clearkey', {
            detail: getClearkey(response)
        }));
    } finally {
        return result;
    }
};

// Intercept license requests
const originalFetch = window.fetch;
window.fetch = function (url, options) {
    const result = originalFetch.apply(this, arguments);

    // Check if this looks like a license request
    if (options && options.method === 'POST' && options.body) {
        try {
            // Check if the request body contains license-related data
            const bodyStr = options.body.toString();
            if (bodyStr.includes('license') || url.includes('license') || url.includes('widevine') || url.includes('playready')) {
                console.log("[LICENSE REQUEST] " + url);

                // Convert headers to a simple object
                let headers = {};
                if (options.headers) {
                    if (options.headers instanceof Headers) {
                        for (let [key, value] of options.headers.entries()) {
                            headers[key] = value;
                        }
                    } else {
                        headers = options.headers;
                    }
                }

                // Send license request data to content script
                document.dispatchEvent(new CustomEvent('license', {
                    detail: {
                        url: url,
                        method: options.method,
                        headers: JSON.stringify(headers),
                        body: btoa(String.fromCharCode(...new Uint8Array(options.body)))
                    }
                }));
            }
        } catch (e) {
            console.log("Error processing license request:", e);
        }
    }

    return result;
};
